#!/usr/bin/env python3
"""
<PERSON>ript to fix user passwords and ensure they work correctly
"""

from db import get_connection
import psycopg2.extras
import bcrypt

def fix_user_passwords():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Define correct passwords for test users
        users_to_fix = [
            ("<EMAIL>", "admin123"),
            ("<EMAIL>", "staff123"),
            ("<EMAIL>", "student123"),
            ("<EMAIL>", "salma123")
        ]
        
        for email, password in users_to_fix:
            print(f"Fixing password for {email}...")
            
            # Hash the password properly
            hashed_password = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
            
            # Update the password in database
            cur.execute("""
                UPDATE users 
                SET password_hash = %s, updated_at = CURRENT_TIMESTAMP
                WHERE email = %s
            """, (hashed_password, email))
            
            print(f"  ✅ Password updated for {email}")
        
        conn.commit()
        
        # Test authentication after fixing
        print("\nTesting authentication after fixes:")
        for email, password in users_to_fix:
            cur.execute("SELECT password_hash FROM users WHERE email = %s", (email,))
            result = cur.fetchone()
            
            if result:
                stored_hash = result['password_hash']
                check_result = bcrypt.checkpw(password.encode(), stored_hash.encode())
                print(f"  {email}: {'✅ PASS' if check_result else '❌ FAIL'}")
            else:
                print(f"  {email}: ❌ USER NOT FOUND")
        
        conn.close()
        print("\nPassword fixes completed!")
        
    except Exception as e:
        print(f"Error fixing passwords: {e}")

if __name__ == "__main__":
    fix_user_passwords()
