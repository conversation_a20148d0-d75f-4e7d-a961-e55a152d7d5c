#!/usr/bin/env python3
"""
Professional UI Components for Alumni App
Provides consistent styling and reusable components
"""

import streamlit as st
from datetime import datetime, date
import pandas as pd

def load_professional_css():
    """Load professional CSS styling for the entire application"""
    st.markdown("""
    <style>
    /* Import Professional Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

    /* Professional Color System */
    :root {
        --primary-50: #eff6ff;
        --primary-100: #dbeafe;
        --primary-500: #3b82f6;
        --primary-600: #2563eb;
        --primary-700: #1d4ed8;
        --primary-900: #1e3a8a;
        
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-300: #d1d5db;
        --gray-400: #9ca3af;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --gray-900: #111827;
        
        --success-50: #ecfdf5;
        --success-500: #10b981;
        --success-600: #059669;
        
        --warning-50: #fffbeb;
        --warning-500: #f59e0b;
        --warning-600: #d97706;
        
        --error-50: #fef2f2;
        --error-500: #ef4444;
        --error-600: #dc2626;
        
        --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    /* Global Styles */
    .main {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        background-color: var(--gray-50);
        padding: 0;
    }

    /* Header Styles */
    .app-header {
        background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
        color: white;
        padding: 2rem 2rem 1rem 2rem;
        margin: -1rem -1rem 2rem -1rem;
        border-radius: 0 0 1rem 1rem;
        box-shadow: var(--shadow-lg);
    }

    .app-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .app-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0.5rem 0 0 0;
        font-weight: 400;
    }

    /* Card Components */
    .professional-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        transition: all 0.2s ease;
    }

    .professional-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-1px);
    }

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .card-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-900);
        margin: 0;
    }

    .card-badge {
        background: var(--primary-100);
        color: var(--primary-700);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Button Styles */
    .stButton > button {
        background: var(--primary-600);
        color: white;
        border: none;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        box-shadow: var(--shadow-xs);
    }

    .stButton > button:hover {
        background: var(--primary-700);
        box-shadow: var(--shadow-sm);
        transform: translateY(-1px);
    }

    /* Success Button */
    .success-button > button {
        background: var(--success-600) !important;
        color: white !important;
    }

    .success-button > button:hover {
        background: var(--success-700) !important;
    }

    /* Warning Button */
    .warning-button > button {
        background: var(--warning-600) !important;
        color: white !important;
    }

    /* Danger Button */
    .danger-button > button {
        background: var(--error-600) !important;
        color: white !important;
    }

    /* Tab Styles */
    .stTabs [data-baseweb="tab-list"] {
        gap: 0.5rem;
        background: var(--gray-100);
        padding: 0.25rem;
        border-radius: 0.75rem;
    }

    .stTabs [data-baseweb="tab"] {
        background: transparent;
        border-radius: 0.5rem;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        color: var(--gray-600);
        border: none;
        transition: all 0.2s ease;
    }

    .stTabs [aria-selected="true"] {
        background: white;
        color: var(--primary-600);
        box-shadow: var(--shadow-xs);
    }

    /* Metric Styles */
    .metric-container {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        transition: all 0.2s ease;
    }

    .metric-container:hover {
        box-shadow: var(--shadow-md);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-600);
        margin: 0;
    }

    .metric-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        font-weight: 500;
        margin: 0.5rem 0 0 0;
    }

    /* Alert Styles */
    .alert-success {
        background: var(--success-50);
        border: 1px solid var(--success-200);
        color: var(--success-800);
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }

    .alert-warning {
        background: var(--warning-50);
        border: 1px solid var(--warning-200);
        color: var(--warning-800);
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }

    .alert-error {
        background: var(--error-50);
        border: 1px solid var(--error-200);
        color: var(--error-800);
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }

    /* Form Styles */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea,
    .stSelectbox > div > div > select {
        border: 1px solid var(--gray-300);
        border-radius: 0.5rem;
        padding: 0.75rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;
    }

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus,
    .stSelectbox > div > div > select:focus {
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
    }

    /* Sidebar Styles */
    .css-1d391kg {
        background: white;
        border-right: 1px solid var(--gray-200);
    }

    /* Hide Streamlit Branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Custom Spacing */
    .section-spacing {
        margin: 2rem 0;
    }

    /* Status Badges */
    .status-active {
        background: var(--success-100);
        color: var(--success-700);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-inactive {
        background: var(--gray-100);
        color: var(--gray-700);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .status-pending {
        background: var(--warning-100);
        color: var(--warning-700);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    /* Data Table Styles */
    .dataframe {
        border: 1px solid var(--gray-200);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .dataframe th {
        background: var(--gray-50);
        color: var(--gray-700);
        font-weight: 600;
        padding: 0.75rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .dataframe td {
        padding: 0.75rem;
        border-bottom: 1px solid var(--gray-100);
    }

    /* Loading Spinner */
    .stSpinner > div {
        border-color: var(--primary-600);
    }
    </style>
    """, unsafe_allow_html=True)

def create_page_header(title, subtitle=None, icon=None):
    """Create a professional page header"""
    icon_html = f'<span style="margin-right: 0.5rem;">{icon}</span>' if icon else ''
    subtitle_html = f'<p class="app-subtitle">{subtitle}</p>' if subtitle else ''
    
    st.markdown(f"""
    <div class="app-header">
        <h1 class="app-title">{icon_html}{title}</h1>
        {subtitle_html}
    </div>
    """, unsafe_allow_html=True)

def create_metric_card(value, label, icon=None, color="primary"):
    """Create a professional metric card"""
    icon_html = f'<div style="font-size: 1.5rem; margin-bottom: 0.5rem;">{icon}</div>' if icon else ''
    
    return f"""
    <div class="metric-container">
        {icon_html}
        <div class="metric-value" style="color: var(--{color}-600);">{value}</div>
        <div class="metric-label">{label}</div>
    </div>
    """

def create_status_badge(status, text=None):
    """Create a status badge"""
    display_text = text or status.title()
    return f'<span class="status-{status.lower()}">{display_text}</span>'

def create_professional_card(title, content, badge=None, actions=None):
    """Create a professional card component"""
    badge_html = f'<span class="card-badge">{badge}</span>' if badge else ''
    actions_html = actions if actions else ''
    
    return f"""
    <div class="professional-card">
        <div class="card-header">
            <h3 class="card-title">{title}</h3>
            {badge_html}
        </div>
        <div class="card-content">
            {content}
        </div>
        {actions_html}
    </div>
    """

def show_alert(message, type="info"):
    """Show a professional alert"""
    st.markdown(f'<div class="alert-{type}">{message}</div>', unsafe_allow_html=True)

def create_action_buttons(buttons):
    """Create a row of action buttons"""
    cols = st.columns(len(buttons))
    for i, (label, key, style) in enumerate(buttons):
        with cols[i]:
            if style == "success":
                st.markdown('<div class="success-button">', unsafe_allow_html=True)
            elif style == "warning":
                st.markdown('<div class="warning-button">', unsafe_allow_html=True)
            elif style == "danger":
                st.markdown('<div class="danger-button">', unsafe_allow_html=True)
            
            result = st.button(label, key=key)
            
            if style in ["success", "warning", "danger"]:
                st.markdown('</div>', unsafe_allow_html=True)
            
            if result:
                return key
    return None
