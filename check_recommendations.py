#!/usr/bin/env python3
"""
Script to check recommendations table structure
"""

from db import get_connection
import psycopg2.extras

def check_recommendations_table():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Check recommendations table structure
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'recommendations'
            ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print('RECOMMENDATIONS table structure:')
        for col in columns:
            print(f'  {col["column_name"]} ({col["data_type"]})')

        # Check sample data
        cur.execute("SELECT * FROM recommendations LIMIT 3")
        recommendations = cur.fetchall()
        print(f'\nSample recommendations ({len(recommendations)} found):')
        for rec in recommendations:
            print(f'  {rec}')

        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_recommendations_table()
