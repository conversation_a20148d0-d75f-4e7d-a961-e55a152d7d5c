#!/usr/bin/env python3
"""
Create sample events for testing the enhanced event management system
"""

from db import get_connection
import psycopg2.extras
from datetime import datetime, date, timedelta

def create_sample_events():
    """Create sample events with various scenarios"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print("🎉 Creating sample events for testing...")
        
        # Sample events with different scenarios
        sample_events = [
            {
                'title': 'Alumni Networking Night 2025',
                'description': 'Join us for an evening of networking, reconnecting with fellow alumni, and sharing career experiences. Light refreshments will be provided.',
                'event_date': date.today() + timedelta(days=30),
                'location': 'University Main Hall',
                'registration_deadline': date.today() + timedelta(days=25),
                'max_participants': 100
            },
            {
                'title': 'Tech Career Fair',
                'description': 'Connect with top tech companies and explore career opportunities. Bring your resume and be ready to network with industry professionals.',
                'event_date': date.today() + timedelta(days=45),
                'location': 'Conference Center',
                'registration_deadline': date.today() + timedelta(days=40),
                'max_participants': 200
            },
            {
                'title': 'AI & Machine Learning Workshop',
                'description': 'Hands-on workshop covering the latest trends in AI and ML. Perfect for both beginners and experienced professionals.',
                'event_date': date.today() + timedelta(days=15),
                'location': 'Computer Science Building - Lab 101',
                'registration_deadline': date.today() + timedelta(days=10),
                'max_participants': 50
            },
            {
                'title': 'Entrepreneurship Seminar',
                'description': 'Learn from successful alumni entrepreneurs about starting and scaling your own business. Q&A session included.',
                'event_date': date.today() + timedelta(days=60),
                'location': 'Business School Auditorium',
                'registration_deadline': date.today() + timedelta(days=55),
                'max_participants': 75
            },
            {
                'title': 'Alumni Sports Day',
                'description': 'Fun sports activities and competitions for all alumni and their families. Various sports including football, basketball, and volleyball.',
                'event_date': date.today() + timedelta(days=90),
                'location': 'University Sports Complex',
                'registration_deadline': date.today() + timedelta(days=85),
                'max_participants': 150
            },
            {
                'title': 'Past Event - Annual Gala 2024',
                'description': 'Our annual gala celebration with awards ceremony and dinner. This is a past event for testing purposes.',
                'event_date': date.today() - timedelta(days=30),
                'location': 'Grand Hotel Ballroom',
                'registration_deadline': date.today() - timedelta(days=35),
                'max_participants': 300
            },
            {
                'title': 'Closed Registration - Leadership Summit',
                'description': 'Leadership development summit for senior professionals. Registration deadline has passed.',
                'event_date': date.today() + timedelta(days=20),
                'location': 'Executive Conference Room',
                'registration_deadline': date.today() - timedelta(days=2),
                'max_participants': 30
            },
            {
                'title': 'Open Registration - Virtual Webinar Series',
                'description': 'Monthly webinar series covering various professional development topics. No registration deadline.',
                'event_date': date.today() + timedelta(days=7),
                'location': 'Online - Zoom',
                'registration_deadline': None,
                'max_participants': None
            }
        ]
        
        # Clear existing sample events first
        cur.execute("DELETE FROM events WHERE title LIKE '%2025%' OR title LIKE '%Sample%' OR title LIKE '%Test%'")
        print(f"  🧹 Cleared existing sample events")
        
        # Insert sample events
        for i, event in enumerate(sample_events, 1):
            cur.execute("""
                INSERT INTO events (title, description, event_date, location, registration_deadline, max_participants, created_at)
                VALUES (%(title)s, %(description)s, %(event_date)s, %(location)s, %(registration_deadline)s, %(max_participants)s, CURRENT_TIMESTAMP)
                RETURNING id
            """, event)
            
            event_id = cur.fetchone()['id']
            print(f"  ✅ Created event {i}: {event['title']} (ID: {event_id})")
        
        conn.commit()
        
        # Create some sample registrations
        print(f"\n👥 Creating sample registrations...")
        
        # Get user IDs
        cur.execute("SELECT id, email FROM users")
        users = cur.fetchall()
        
        # Get event IDs
        cur.execute("SELECT id, title FROM events WHERE event_date >= CURRENT_DATE")
        future_events = cur.fetchall()
        
        # Register some users for events
        registrations_created = 0
        for i, event in enumerate(future_events[:4]):  # Register for first 4 future events
            for j, user in enumerate(users):
                if j <= i:  # Gradually increase registrations per event
                    try:
                        cur.execute("""
                            INSERT INTO event_participants (event_id, user_id, registered_at, status)
                            VALUES (%s, %s, CURRENT_TIMESTAMP, 'registered')
                        """, (event['id'], user['id']))
                        registrations_created += 1
                    except:
                        pass  # Skip if already registered
        
        conn.commit()
        print(f"  ✅ Created {registrations_created} sample registrations")
        
        # Show summary
        print(f"\n📊 Event Summary:")
        cur.execute("""
            SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_date >= CURRENT_DATE THEN 1 END) as future_events,
                COUNT(CASE WHEN event_date < CURRENT_DATE THEN 1 END) as past_events
            FROM events
        """)
        summary = cur.fetchone()
        print(f"  Total Events: {summary['total_events']}")
        print(f"  Future Events: {summary['future_events']}")
        print(f"  Past Events: {summary['past_events']}")
        
        # Show registration summary
        cur.execute("""
            SELECT 
                COUNT(*) as total_registrations,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT event_id) as events_with_registrations
            FROM event_participants
        """)
        reg_summary = cur.fetchone()
        print(f"  Total Registrations: {reg_summary['total_registrations']}")
        print(f"  Unique Users Registered: {reg_summary['unique_users']}")
        print(f"  Events with Registrations: {reg_summary['events_with_registrations']}")
        
        # Show sample events
        print(f"\n📋 Sample Events Created:")
        cur.execute("""
            SELECT 
                e.title,
                e.event_date,
                e.registration_deadline,
                e.max_participants,
                COUNT(ep.user_id) as registrations,
                CASE 
                    WHEN e.registration_deadline < CURRENT_DATE THEN 'closed'
                    WHEN e.event_date < CURRENT_DATE THEN 'completed'
                    ELSE 'open'
                END as status
            FROM events e
            LEFT JOIN event_participants ep ON e.id = ep.event_id
            GROUP BY e.id, e.title, e.event_date, e.registration_deadline, e.max_participants
            ORDER BY e.event_date ASC
        """)
        
        events_list = cur.fetchall()
        for event in events_list:
            status_emoji = {'open': '🟢', 'closed': '🟡', 'completed': '🔴'}.get(event['status'], '⚪')
            print(f"  {status_emoji} {event['title']}")
            print(f"    Date: {event['event_date']}")
            print(f"    Status: {event['status'].title()}")
            print(f"    Registrations: {event['registrations']}/{event['max_participants'] or '∞'}")
            print()
        
        conn.close()
        print(f"✅ Sample events created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating sample events: {e}")

def test_event_queries():
    """Test various event queries"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print(f"\n🧪 Testing event queries...")
        
        # Test 1: Get all events with registration counts
        print(f"\n1. Testing events with registration counts...")
        cur.execute("""
            SELECT 
                e.title,
                e.event_date,
                COUNT(ep.user_id) as registration_count,
                e.max_participants,
                CASE 
                    WHEN e.registration_deadline < CURRENT_DATE THEN 'closed'
                    WHEN e.event_date < CURRENT_DATE THEN 'completed'
                    ELSE 'open'
                END as status
            FROM events e
            LEFT JOIN event_participants ep ON e.id = ep.event_id
            GROUP BY e.id, e.title, e.event_date, e.registration_deadline, e.max_participants
            ORDER BY e.event_date ASC
            LIMIT 3
        """)
        
        events = cur.fetchall()
        for event in events:
            print(f"  📅 {event['title']}: {event['registration_count']} registrations, Status: {event['status']}")
        
        # Test 2: Get user registrations
        print(f"\n2. Testing user registrations...")
        cur.execute("""
            SELECT 
                u.email,
                e.title,
                ep.registered_at,
                ep.status
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            JOIN events e ON ep.event_id = e.id
            ORDER BY ep.registered_at DESC
            LIMIT 3
        """)
        
        registrations = cur.fetchall()
        for reg in registrations:
            print(f"  👤 {reg['email']} registered for '{reg['title']}' on {reg['registered_at'].strftime('%Y-%m-%d')}")
        
        # Test 3: Check event capacity
        print(f"\n3. Testing event capacity checks...")
        cur.execute("""
            SELECT 
                e.title,
                e.max_participants,
                COUNT(ep.user_id) as current_registrations,
                CASE 
                    WHEN e.max_participants IS NULL THEN 'unlimited'
                    WHEN COUNT(ep.user_id) >= e.max_participants THEN 'full'
                    ELSE 'available'
                END as capacity_status
            FROM events e
            LEFT JOIN event_participants ep ON e.id = ep.event_id
            WHERE e.event_date >= CURRENT_DATE
            GROUP BY e.id, e.title, e.max_participants
            ORDER BY current_registrations DESC
            LIMIT 3
        """)
        
        capacity_checks = cur.fetchall()
        for check in capacity_checks:
            print(f"  🎯 {check['title']}: {check['current_registrations']}/{check['max_participants'] or '∞'} - {check['capacity_status']}")
        
        conn.close()
        print(f"\n✅ Event queries test completed!")
        
    except Exception as e:
        print(f"❌ Error testing event queries: {e}")

def main():
    create_sample_events()
    test_event_queries()

if __name__ == "__main__":
    main()
