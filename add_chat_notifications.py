#!/usr/bin/env python3
"""
Script to add notification features to the chat system
"""

from db import get_connection
import psycopg2.extras

def add_chat_notification_features():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Check if is_read column exists in chats table
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chats' AND column_name = 'is_read'
        """)
        
        if not cur.fetchone():
            print("Adding is_read column to chats table...")
            cur.execute("ALTER TABLE chats ADD COLUMN is_read BOOLEAN DEFAULT FALSE")
            conn.commit()
            print("is_read column added successfully!")
        else:
            print("is_read column already exists.")

        # Check if notification_sent column exists
        cur.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chats' AND column_name = 'notification_sent'
        """)
        
        if not cur.fetchone():
            print("Adding notification_sent column to chats table...")
            cur.execute("ALTER TABLE chats ADD COLUMN notification_sent BOOLEAN DEFAULT FALSE")
            conn.commit()
            print("notification_sent column added successfully!")
        else:
            print("notification_sent column already exists.")

        # Show current chats table structure
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'chats'
            ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print('\nCHATS table structure:')
        for col in columns:
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f'  {col["column_name"]} ({col["data_type"]}) - Nullable: {col["is_nullable"]}{default}')

        conn.close()
        print("\nChat notification features setup completed!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    add_chat_notification_features()
