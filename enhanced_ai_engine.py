#!/usr/bin/env python3
"""
Enhanced AI Recommendation Engine for Alumni App
Uses advanced ML models and multiple recommendation strategies
"""

import pandas as pd
import numpy as np
import json
import os
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.ensemble import RandomForestClassifier
from sklearn.naive_bayes import MultinomialNB
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, classification_report
import joblib
from db import get_connection
import psycopg2.extras

class EnhancedRecommendationEngine:
    def __init__(self):
        self.models = {}
        self.vectorizers = {}
        self.interest_categories = self.load_interest_categories()
        self.training_data = None
        
    def load_interest_categories(self):
        """Load interest categories from JSON file"""
        try:
            with open('data/interest_categories.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return {}
    
    def load_training_data(self):
        """Load enhanced training data"""
        try:
            self.training_data = pd.read_csv('data/enhanced_training_data.csv')
            return True
        except FileNotFoundError:
            print("Enhanced training data not found. Please ensure enhanced_training_data.csv exists.")
            return False
    
    def preprocess_interests(self, interests):
        """Convert user interests to standardized format"""
        if isinstance(interests, list):
            interests = ', '.join(interests)
        
        # Clean and standardize interests
        interests = interests.lower().strip()
        
        # Map to standard categories
        standardized_interests = []
        for category, data in self.interest_categories.items():
            for subcategory in data['subcategories']:
                if subcategory.lower() in interests:
                    standardized_interests.append(subcategory)
        
        return ', '.join(standardized_interests) if standardized_interests else interests
    
    def train_content_based_model(self):
        """Train content-based recommendation model using TF-IDF"""
        if not self.load_training_data():
            return False
        
        # Create TF-IDF vectorizer
        tfidf = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2
        )
        
        # Fit on user interests and recommendation text
        text_data = self.training_data['user_interests'] + ' ' + self.training_data['recommendation_text']
        tfidf_matrix = tfidf.fit_transform(text_data)
        
        # Train classification model
        X = tfidf.transform(self.training_data['user_interests'])
        y = self.training_data['recommendation_type']
        
        # Try multiple models
        models = {
            'random_forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'naive_bayes': MultinomialNB(),
            'logistic_regression': LogisticRegression(random_state=42, max_iter=1000)
        }
        
        best_model = None
        best_score = 0
        
        for name, model in models.items():
            scores = cross_val_score(model, X, y, cv=5)
            avg_score = scores.mean()
            print(f"{name}: {avg_score:.3f} (+/- {scores.std() * 2:.3f})")
            
            if avg_score > best_score:
                best_score = avg_score
                best_model = model
        
        # Train best model on full data
        best_model.fit(X, y)
        
        # Save model and vectorizer
        os.makedirs('models', exist_ok=True)
        joblib.dump(best_model, 'models/content_based_model.pkl')
        joblib.dump(tfidf, 'models/tfidf_vectorizer.pkl')
        
        self.models['content_based'] = best_model
        self.vectorizers['tfidf'] = tfidf
        
        print(f"Content-based model trained with accuracy: {best_score:.3f}")
        return True
    
    def get_content_based_recommendations(self, user_interests, num_recommendations=5):
        """Get content-based recommendations"""
        try:
            # Load training data if not loaded
            if self.training_data is None:
                if not self.load_training_data():
                    return []

            # Load model if not in memory
            if 'content_based' not in self.models:
                try:
                    self.models['content_based'] = joblib.load('models/content_based_model.pkl')
                    self.vectorizers['tfidf'] = joblib.load('models/tfidf_vectorizer.pkl')
                except FileNotFoundError:
                    # Train model if not found
                    if not self.train_content_based_model():
                        return []

            # Preprocess user interests
            processed_interests = self.preprocess_interests(user_interests)
            if not processed_interests:
                processed_interests = str(user_interests)

            # Transform interests
            user_vector = self.vectorizers['tfidf'].transform([processed_interests])

            # Predict recommendation types
            predicted_types = self.models['content_based'].predict(user_vector)

            # Get recommendations from training data
            recommendations = []

            # If prediction fails, use similarity matching
            if len(predicted_types) == 0:
                # Fallback to similarity matching
                return self.get_similarity_based_recommendations(user_interests, num_recommendations)

            for rec_type in predicted_types:
                type_recommendations = self.training_data[
                    self.training_data['recommendation_type'] == rec_type
                ].sort_values('priority', ascending=False)

                for _, row in type_recommendations.head(2).iterrows():
                    recommendations.append({
                        'type': row['recommendation_type'],
                        'text': row['recommendation_text'],
                        'category': row['category'],
                        'priority': row['priority']
                    })

            # If no recommendations from prediction, use similarity
            if not recommendations:
                return self.get_similarity_based_recommendations(user_interests, num_recommendations)

            return recommendations[:num_recommendations]

        except Exception as e:
            print(f"Error in content-based recommendations: {e}")
            # Fallback to similarity-based recommendations
            return self.get_similarity_based_recommendations(user_interests, num_recommendations)

    def get_similarity_based_recommendations(self, user_interests, num_recommendations=5):
        """Get recommendations based on text similarity"""
        try:
            if self.training_data is None:
                if not self.load_training_data():
                    return []

            # Preprocess user interests
            processed_interests = self.preprocess_interests(user_interests)
            if not processed_interests:
                processed_interests = str(user_interests)

            # Create TF-IDF vectors for similarity comparison
            vectorizer = TfidfVectorizer(stop_words='english', max_features=100)

            # Combine user interests and training data
            all_texts = [processed_interests] + list(self.training_data['user_interests'])
            tfidf_matrix = vectorizer.fit_transform(all_texts)

            # Calculate similarity between user interests and training data
            user_vector = tfidf_matrix[0:1]
            training_vectors = tfidf_matrix[1:]

            similarities = cosine_similarity(user_vector, training_vectors).flatten()

            # Get top similar recommendations
            top_indices = similarities.argsort()[-num_recommendations:][::-1]

            recommendations = []
            for idx in top_indices:
                if similarities[idx] > 0.1:  # Minimum similarity threshold
                    row = self.training_data.iloc[idx]
                    recommendations.append({
                        'type': row['recommendation_type'],
                        'text': row['recommendation_text'],
                        'category': row['category'],
                        'priority': row['priority'],
                        'similarity': similarities[idx]
                    })

            return recommendations

        except Exception as e:
            print(f"Error in similarity-based recommendations: {e}")
            return []

    def get_collaborative_recommendations(self, user_email, num_recommendations=3):
        """Get collaborative filtering recommendations based on similar users"""
        try:
            conn = get_connection()
            cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Get current user's interests and activities
            cur.execute("""
                SELECT interests, department, role 
                FROM users 
                WHERE email = %s
            """, (user_email,))
            current_user = cur.fetchone()
            
            if not current_user:
                return []
            
            # Find similar users based on interests and department
            cur.execute("""
                SELECT email, interests, department, role
                FROM users 
                WHERE email != %s 
                AND (department = %s OR interests && %s)
                LIMIT 10
            """, (user_email, current_user['department'], current_user['interests']))
            
            similar_users = cur.fetchall()
            
            recommendations = []
            
            # Get recommendations based on similar users' activities
            for user in similar_users:
                # Check what events they've attended
                cur.execute("""
                    SELECT e.title, e.description 
                    FROM events e
                    JOIN event_participants ep ON e.id = ep.event_id
                    JOIN users u ON ep.user_id = u.id
                    WHERE u.email = %s
                    LIMIT 2
                """, (user['email'],))
                
                events = cur.fetchall()
                for event in events:
                    recommendations.append({
                        'type': 'event',
                        'text': f"Similar alumni attended: {event['title']}",
                        'category': 'collaborative',
                        'priority': 3
                    })
            
            conn.close()
            return recommendations[:num_recommendations]
            
        except Exception as e:
            print(f"Error in collaborative recommendations: {e}")
            return []
    
    def get_networking_recommendations(self, user_email):
        """Get recommendations for who to contact/chat with"""
        try:
            conn = get_connection()
            cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            # Get current user info
            cur.execute("""
                SELECT interests, department, role 
                FROM users 
                WHERE email = %s
            """, (user_email,))
            current_user = cur.fetchone()
            
            if not current_user:
                return []
            
            recommendations = []
            
            # Find users with similar interests but different roles (for mentoring)
            cur.execute("""
                SELECT email, firstname, lastname, role, department
                FROM users 
                WHERE email != %s 
                AND interests && %s
                AND role != %s
                ORDER BY 
                    CASE 
                        WHEN role = 'admin' THEN 1
                        WHEN role = 'staff' THEN 2
                        ELSE 3
                    END
                LIMIT 3
            """, (user_email, current_user['interests'], current_user['role']))
            
            potential_contacts = cur.fetchall()
            
            for contact in potential_contacts:
                role_description = {
                    'admin': 'Administrator - Great for career guidance',
                    'staff': 'Faculty/Staff - Academic and research insights',
                    'student': 'Fellow Student - Peer collaboration'
                }.get(contact['role'], 'Alumni')
                
                recommendations.append({
                    'type': 'contact',
                    'text': f"Connect with {contact['firstname']} {contact['lastname']} ({role_description})",
                    'category': 'networking',
                    'priority': 4,
                    'email': contact['email']
                })
            
            conn.close()
            return recommendations
            
        except Exception as e:
            print(f"Error in networking recommendations: {e}")
            return []
    
    def get_reading_recommendations(self, user_interests):
        """Get reading recommendations based on interests"""
        try:
            if self.training_data is None:
                if not self.load_training_data():
                    return []

            reading_recs = self.training_data[
                self.training_data['recommendation_type'] == 'reading'
            ]

            if reading_recs.empty:
                return []

            recommendations = []
            processed_interests = self.preprocess_interests(user_interests)
            if not processed_interests:
                processed_interests = str(user_interests)

            processed_interests = processed_interests.lower()

            for _, row in reading_recs.iterrows():
                # Check if any user interest matches the recommendation's target interests
                if any(interest.strip().lower() in row['user_interests'].lower()
                       for interest in processed_interests.split(',') if interest.strip()):
                    recommendations.append({
                        'type': 'reading',
                        'text': row['recommendation_text'],
                        'category': 'resources',
                        'priority': row['priority']
                    })

            # If no matches found, return top reading recommendations
            if not recommendations:
                for _, row in reading_recs.head(3).iterrows():
                    recommendations.append({
                        'type': 'reading',
                        'text': row['recommendation_text'],
                        'category': 'resources',
                        'priority': row['priority']
                    })

            return sorted(recommendations, key=lambda x: x['priority'], reverse=True)[:3]

        except Exception as e:
            print(f"Error in reading recommendations: {e}")
            return []
    
    def get_comprehensive_recommendations(self, user_email):
        """Get comprehensive recommendations combining all methods"""
        try:
            # Get user data
            conn = get_connection()
            cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            
            cur.execute("""
                SELECT interests, department, role, firstname, lastname
                FROM users 
                WHERE email = %s
            """, (user_email,))
            user = cur.fetchone()
            conn.close()
            
            if not user or not user['interests']:
                return {
                    'content_based': [],
                    'collaborative': [],
                    'networking': [],
                    'reading': [],
                    'personalized_message': "Please update your interests to get personalized recommendations."
                }
            
            # Get different types of recommendations
            content_recs = self.get_content_based_recommendations(user['interests'])
            collaborative_recs = self.get_collaborative_recommendations(user_email)
            networking_recs = self.get_networking_recommendations(user_email)
            reading_recs = self.get_reading_recommendations(user['interests'])
            
            # Create personalized message
            interests_str = ', '.join(user['interests']) if isinstance(user['interests'], list) else str(user['interests'])
            personalized_message = f"Hi {user['firstname']}! Based on your interests in {interests_str}, here are your personalized recommendations:"
            
            return {
                'content_based': content_recs,
                'collaborative': collaborative_recs,
                'networking': networking_recs,
                'reading': reading_recs,
                'personalized_message': personalized_message
            }
            
        except Exception as e:
            print(f"Error getting comprehensive recommendations: {e}")
            return {
                'content_based': [],
                'collaborative': [],
                'networking': [],
                'reading': [],
                'personalized_message': "Error generating recommendations. Please try again."
            }

# Initialize the engine
recommendation_engine = EnhancedRecommendationEngine()

def train_enhanced_models():
    """Train all enhanced models"""
    print("Training enhanced AI recommendation models...")
    success = recommendation_engine.train_content_based_model()
    if success:
        print("Enhanced AI models trained successfully!")
    return success

def get_enhanced_recommendations(user_email):
    """Get enhanced recommendations for a user"""
    return recommendation_engine.get_comprehensive_recommendations(user_email)
