#!/usr/bin/env python3
"""
Complete Communication and Email System
Includes contact organizer, email participants, and messaging features
"""

import streamlit as st
from datetime import datetime
from db import get_connection
import psycopg2.extras
from ui_components import create_professional_card, show_alert, create_action_buttons
import pandas as pd

def create_messages_table():
    """Create messages table for internal communication"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        
        cur.execute("""
            CREATE TABLE IF NOT EXISTS internal_messages (
                id SERIAL PRIMARY KEY,
                sender_id INTEGER REFERENCES users(id),
                recipient_id INTEGER REFERENCES users(id),
                subject VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                message_type VARCHAR(50) DEFAULT 'general',
                event_id INTEGER REFERENCES events(id),
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                read_at TIMESTAMP,
                is_read BOOLEAN DEFAULT FALSE,
                priority VARCHAR(20) DEFAULT 'normal'
            )
        """)
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error creating messages table: {e}")
        return False

def send_internal_message(sender_email, recipient_email, subject, message, message_type="general", event_id=None, priority="normal"):
    """Send internal message between users"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get sender and recipient IDs
        cur.execute("SELECT id FROM users WHERE email = %s", (sender_email,))
        sender = cur.fetchone()
        
        cur.execute("SELECT id FROM users WHERE email = %s", (recipient_email,))
        recipient = cur.fetchone()
        
        if not sender or not recipient:
            return False, "Sender or recipient not found"
        
        # Insert message
        cur.execute("""
            INSERT INTO internal_messages (sender_id, recipient_id, subject, message, message_type, event_id, priority)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
        """, (sender['id'], recipient['id'], subject, message, message_type, event_id, priority))
        
        message_id = cur.fetchone()['id']
        
        conn.commit()
        conn.close()
        
        return True, f"Message sent successfully (ID: {message_id})"
        
    except Exception as e:
        return False, f"Error sending message: {e}"

def get_user_messages(user_email, message_type="all"):
    """Get messages for a user"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get user ID
        cur.execute("SELECT id FROM users WHERE email = %s", (user_email,))
        user = cur.fetchone()
        
        if not user:
            return []
        
        # Build query based on message type
        where_clause = "WHERE im.recipient_id = %s"
        params = [user['id']]
        
        if message_type != "all":
            where_clause += " AND im.message_type = %s"
            params.append(message_type)
        
        cur.execute(f"""
            SELECT 
                im.*,
                u.firstname as sender_firstname,
                u.lastname as sender_lastname,
                u.email as sender_email,
                e.title as event_title
            FROM internal_messages im
            JOIN users u ON im.sender_id = u.id
            LEFT JOIN events e ON im.event_id = e.id
            {where_clause}
            ORDER BY im.sent_at DESC
        """, params)
        
        messages = cur.fetchall()
        conn.close()
        
        return messages
        
    except Exception as e:
        print(f"Error getting messages: {e}")
        return []

def mark_message_as_read(message_id, user_email):
    """Mark message as read"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get user ID
        cur.execute("SELECT id FROM users WHERE email = %s", (user_email,))
        user = cur.fetchone()
        
        if not user:
            return False, "User not found"
        
        # Update message
        cur.execute("""
            UPDATE internal_messages 
            SET is_read = TRUE, read_at = CURRENT_TIMESTAMP
            WHERE id = %s AND recipient_id = %s
        """, (message_id, user['id']))
        
        conn.commit()
        conn.close()
        
        return True, "Message marked as read"
        
    except Exception as e:
        return False, f"Error marking message as read: {e}"

def get_event_organizer_contact(event_id):
    """Get event organizer contact information"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # For now, we'll assume the first admin is the organizer
        # In a real system, you'd have an organizer field in the events table
        cur.execute("""
            SELECT u.firstname, u.lastname, u.email, u.department
            FROM users u
            WHERE u.role = 'admin'
            ORDER BY u.created_at ASC
            LIMIT 1
        """)
        
        organizer = cur.fetchone()
        conn.close()
        
        return organizer
        
    except Exception as e:
        print(f"Error getting event organizer: {e}")
        return None

def send_bulk_email_to_participants(event_id, sender_email, subject, message):
    """Send bulk email to all event participants"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get all participants for the event
        cur.execute("""
            SELECT DISTINCT u.email, u.firstname, u.lastname
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            WHERE ep.event_id = %s AND u.is_active = TRUE
        """, (event_id,))
        
        participants = cur.fetchall()
        
        if not participants:
            return False, "No participants found for this event"
        
        # Send message to each participant
        sent_count = 0
        failed_count = 0
        
        for participant in participants:
            success, _ = send_internal_message(
                sender_email=sender_email,
                recipient_email=participant['email'],
                subject=subject,
                message=message,
                message_type="event_notification",
                event_id=event_id,
                priority="normal"
            )
            
            if success:
                sent_count += 1
            else:
                failed_count += 1
        
        return True, f"Bulk email sent to {sent_count} participants. {failed_count} failed."
        
    except Exception as e:
        return False, f"Error sending bulk email: {e}"

def show_contact_organizer_form(event_id, event_title, user_email):
    """Show contact organizer form"""
    st.markdown(f"### 📧 Contact Event Organizer")
    st.markdown(f"**Event:** {event_title}")
    
    # Get organizer info
    organizer = get_event_organizer_contact(event_id)
    
    if not organizer:
        st.error("Event organizer contact information not available")
        return
    
    st.info(f"**Organizer:** {organizer['firstname']} {organizer['lastname']} ({organizer['department']})")
    
    with st.form("contact_organizer"):
        subject = st.text_input(
            "Subject", 
            value=f"Question about {event_title}",
            placeholder="Enter message subject"
        )
        
        message = st.text_area(
            "Message",
            placeholder="Enter your message to the event organizer...",
            height=150
        )
        
        priority = st.selectbox("Priority", ["normal", "high", "urgent"], index=0)
        
        submit = st.form_submit_button("Send Message", use_container_width=True)
        
        if submit:
            if not subject or not message:
                st.error("Subject and message are required")
            else:
                success, result_message = send_internal_message(
                    sender_email=user_email,
                    recipient_email=organizer['email'],
                    subject=subject,
                    message=message,
                    message_type="event_inquiry",
                    event_id=event_id,
                    priority=priority
                )
                
                if success:
                    st.success("Message sent to event organizer!")
                    st.balloons()
                else:
                    st.error(result_message)

def show_participant_email_form(event_id, event_title, sender_email):
    """Show form to email all participants"""
    st.markdown(f"### 📧 Email All Participants")
    st.markdown(f"**Event:** {event_title}")
    
    # Get participant count
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("SELECT COUNT(*) FROM event_participants WHERE event_id = %s", (event_id,))
        participant_count = cur.fetchone()[0]
        conn.close()
        
        st.info(f"This will send an email to {participant_count} registered participants")
        
    except Exception as e:
        st.error(f"Error getting participant count: {e}")
        return
    
    with st.form("email_participants"):
        subject = st.text_input(
            "Subject",
            value=f"Update regarding {event_title}",
            placeholder="Enter email subject"
        )
        
        message = st.text_area(
            "Message",
            placeholder="Enter your message to all participants...",
            height=200
        )
        
        # Message templates
        st.markdown("**Quick Templates:**")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("Event Reminder"):
                st.session_state['email_template'] = f"Reminder: {event_title} is coming up soon. Please make sure to attend on time."
        
        with col2:
            if st.button("Event Update"):
                st.session_state['email_template'] = f"Important update regarding {event_title}. Please read the details below."
        
        with col3:
            if st.button("Event Cancellation"):
                st.session_state['email_template'] = f"We regret to inform you that {event_title} has been cancelled. We apologize for any inconvenience."
        
        # Apply template if selected
        if 'email_template' in st.session_state:
            message = st.text_area(
                "Message",
                value=st.session_state['email_template'],
                height=200,
                key="template_message"
            )
            del st.session_state['email_template']
        
        confirm_send = st.checkbox("I confirm that I want to send this email to all participants")
        
        submit = st.form_submit_button("Send Bulk Email", use_container_width=True)
        
        if submit:
            if not confirm_send:
                st.error("Please confirm that you want to send the bulk email")
            elif not subject or not message:
                st.error("Subject and message are required")
            else:
                with st.spinner("Sending emails to all participants..."):
                    success, result_message = send_bulk_email_to_participants(
                        event_id=event_id,
                        sender_email=sender_email,
                        subject=subject,
                        message=message
                    )
                
                if success:
                    st.success(result_message)
                    st.balloons()
                else:
                    st.error(result_message)

def show_user_messages_inbox(user_email):
    """Show user's message inbox"""
    st.markdown("### 📬 Message Inbox")
    
    # Create messages table if it doesn't exist
    create_messages_table()
    
    # Get messages
    messages = get_user_messages(user_email)
    
    if not messages:
        st.info("No messages in your inbox")
        return
    
    # Filter options
    col1, col2 = st.columns(2)
    
    with col1:
        message_filter = st.selectbox(
            "Filter by Type",
            ["all", "general", "event_inquiry", "event_notification"]
        )
    
    with col2:
        read_filter = st.selectbox(
            "Filter by Status",
            ["all", "unread", "read"]
        )
    
    # Apply filters
    filtered_messages = messages
    
    if message_filter != "all":
        filtered_messages = [m for m in filtered_messages if m['message_type'] == message_filter]
    
    if read_filter == "unread":
        filtered_messages = [m for m in filtered_messages if not m['is_read']]
    elif read_filter == "read":
        filtered_messages = [m for m in filtered_messages if m['is_read']]
    
    st.markdown(f"**Showing {len(filtered_messages)} of {len(messages)} messages**")
    
    # Display messages
    for message in filtered_messages:
        read_status = "📖" if message['is_read'] else "📩"
        priority_icon = {"normal": "", "high": "⚠️", "urgent": "🚨"}.get(message['priority'], "")
        
        with st.expander(f"{read_status} {priority_icon} {message['subject']} - From: {message['sender_firstname']} {message['sender_lastname']}"):
            col1, col2 = st.columns([3, 1])
            
            with col1:
                st.markdown(f"**From:** {message['sender_firstname']} {message['sender_lastname']} ({message['sender_email']})")
                st.markdown(f"**Sent:** {message['sent_at'].strftime('%B %d, %Y at %I:%M %p')}")
                if message['event_title']:
                    st.markdown(f"**Related Event:** {message['event_title']}")
                st.markdown(f"**Priority:** {message['priority'].title()}")
                st.markdown("---")
                st.markdown(message['message'])
            
            with col2:
                if not message['is_read']:
                    if st.button("Mark as Read", key=f"read_{message['id']}"):
                        success, result_message = mark_message_as_read(message['id'], user_email)
                        if success:
                            st.success("Marked as read")
                            st.rerun()
                        else:
                            st.error(result_message)
                
                if st.button("Reply", key=f"reply_{message['id']}"):
                    st.session_state[f"reply_to_{message['id']}"] = True
                    st.rerun()
        
        # Reply form
        if st.session_state.get(f"reply_to_{message['id']}", False):
            with st.form(f"reply_form_{message['id']}"):
                reply_subject = st.text_input("Subject", value=f"Re: {message['subject']}")
                reply_message = st.text_area("Reply Message", height=100)
                
                col1, col2 = st.columns(2)
                with col1:
                    send_reply = st.form_submit_button("Send Reply")
                with col2:
                    cancel_reply = st.form_submit_button("Cancel")
                
                if send_reply:
                    if reply_message:
                        success, result = send_internal_message(
                            sender_email=user_email,
                            recipient_email=message['sender_email'],
                            subject=reply_subject,
                            message=reply_message,
                            message_type="general"
                        )
                        
                        if success:
                            st.success("Reply sent!")
                            del st.session_state[f"reply_to_{message['id']}"]
                            st.rerun()
                        else:
                            st.error(result)
                    else:
                        st.error("Reply message is required")
                
                if cancel_reply:
                    del st.session_state[f"reply_to_{message['id']}"]
                    st.rerun()

def export_participant_emails(event_id):
    """Export participant emails to CSV"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cur.execute("""
            SELECT 
                u.firstname,
                u.lastname,
                u.email,
                u.department,
                u.role,
                ep.registered_at,
                ep.status
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            WHERE ep.event_id = %s
            ORDER BY ep.registered_at ASC
        """, (event_id,))
        
        participants = cur.fetchall()
        conn.close()
        
        if participants:
            df = pd.DataFrame(participants)
            return df.to_csv(index=False)
        else:
            return None
            
    except Exception as e:
        print(f"Error exporting participant emails: {e}")
        return None
