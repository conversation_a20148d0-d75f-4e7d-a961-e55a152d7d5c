#!/usr/bin/env python3
"""
Enhanced Presentation Components
Improved UI/UX for Events and User Management with better visual presentation
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from db import get_connection
import psycopg2.extras
from ui_components import create_professional_card, show_alert, create_status_badge, create_metric_card

def show_enhanced_all_events_tab(user_email, events):
    """Enhanced all events display with better presentation"""
    st.markdown("### 🎉 All Events")
    
    if not events:
        st.markdown("""
        <div style="text-align: center; padding: 3rem; background: linear-gradient(135deg, #f8fafc, #e2e8f0); 
                    border-radius: 12px; margin: 2rem 0;">
            <h3 style="color: #64748b; margin-bottom: 1rem;">📅 No Events Available</h3>
            <p style="color: #94a3b8;">Check back later for upcoming events and activities!</p>
        </div>
        """, unsafe_allow_html=True)
        return

    # Enhanced filter section with better styling
    st.markdown("""
    <div style="background: linear-gradient(135deg, #667eea, #764ba2); padding: 1.5rem; 
                border-radius: 12px; margin-bottom: 2rem;">
        <h4 style="color: white; margin: 0 0 1rem 0;">🔍 Find Your Perfect Event</h4>
        <p style="color: rgba(255,255,255,0.9); margin: 0;">Filter and search through our events</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Filter controls in a professional layout
    col1, col2, col3, col4 = st.columns([2, 2, 2, 2])
    
    with col1:
        status_filter = st.selectbox(
            "📊 Event Status",
            ["All", "Open", "Closed", "Completed"],
            key="event_status_filter",
            help="Filter events by registration status"
        )
    
    with col2:
        date_filter = st.selectbox(
            "📅 Time Period",
            ["All", "Upcoming", "Past", "This Month", "Next Month"],
            key="event_date_filter",
            help="Filter events by date range"
        )
    
    with col3:
        category_filter = st.selectbox(
            "🏷️ Category",
            ["All", "Networking", "Workshop", "Seminar", "Social", "Career"],
            key="event_category_filter",
            help="Filter by event category"
        )
    
    with col4:
        search_term = st.text_input(
            "🔍 Search",
            placeholder="Search events...",
            help="Search by title, description, or location"
        )

    # Apply filters with enhanced logic
    filtered_events = apply_enhanced_filters(events, status_filter, date_filter, category_filter, search_term)
    
    # Results summary with metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("📋 Total Events", len(events))
    with col2:
        st.metric("🎯 Filtered Results", len(filtered_events))
    with col3:
        open_events = len([e for e in filtered_events if e['status'] == 'open'])
        st.metric("🟢 Open for Registration", open_events)

    # Sort options
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown(f"**Showing {len(filtered_events)} events**")
    with col2:
        sort_option = st.selectbox(
            "Sort by:",
            ["Date (Newest)", "Date (Oldest)", "Title A-Z", "Title Z-A", "Registration Count"],
            key="event_sort"
        )
    
    # Apply sorting
    filtered_events = sort_events(filtered_events, sort_option)
    
    # Display events in enhanced cards
    if filtered_events:
        for i, event in enumerate(filtered_events):
            show_enhanced_event_card(user_email, event, i)
    else:
        st.markdown("""
        <div style="text-align: center; padding: 2rem; background: #f8fafc; 
                    border-radius: 8px; border: 2px dashed #cbd5e1;">
            <h4 style="color: #64748b;">🔍 No events match your filters</h4>
            <p style="color: #94a3b8;">Try adjusting your search criteria</p>
        </div>
        """, unsafe_allow_html=True)

def show_enhanced_event_card(user_email, event, index):
    """Enhanced event card with better visual presentation"""
    from event_management import get_user_id_by_email, register_for_event, cancel_registration, can_modify_registration
    
    # Check registration status
    user_id = get_user_id_by_email(user_email)
    is_registered = False
    
    if user_id:
        try:
            conn = get_connection()
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT id FROM event_participants
                    WHERE user_id = %s AND event_id = %s
                """, (user_id, event['id']))
                is_registered = cur.fetchone() is not None
            conn.close()
        except:
            pass
    
    # Status colors and icons
    status_config = {
        'open': {'color': '#10b981', 'bg': '#d1fae5', 'icon': '🟢', 'text': 'Open'},
        'closed': {'color': '#f59e0b', 'bg': '#fef3c7', 'icon': '🟡', 'text': 'Closed'},
        'completed': {'color': '#6b7280', 'bg': '#f3f4f6', 'icon': '⚪', 'text': 'Completed'}
    }
    
    status = status_config.get(event['status'], status_config['open'])
    
    # Calculate days until event
    days_until = (event['event_date'] - date.today()).days
    if days_until > 0:
        time_info = f"In {days_until} days"
        time_color = "#10b981"
    elif days_until == 0:
        time_info = "Today!"
        time_color = "#f59e0b"
    else:
        time_info = f"{abs(days_until)} days ago"
        time_color = "#6b7280"
    
    # Registration capacity
    capacity_info = ""
    if event.get('max_participants'):
        capacity_pct = (event['registration_count'] / event['max_participants']) * 100
        if capacity_pct >= 90:
            capacity_color = "#ef4444"
            capacity_icon = "🔴"
        elif capacity_pct >= 70:
            capacity_color = "#f59e0b"
            capacity_icon = "🟡"
        else:
            capacity_color = "#10b981"
            capacity_icon = "🟢"
        capacity_info = f"{capacity_icon} {event['registration_count']}/{event['max_participants']} ({capacity_pct:.0f}%)"
    else:
        capacity_info = f"👥 {event['registration_count']} registered"
    
    # Enhanced card design
    with st.container():
        st.markdown(f"""
        <div style="background: white; border-radius: 16px; padding: 1.5rem; margin-bottom: 1.5rem;
                    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                    border-left: 4px solid {status['color']}; transition: transform 0.2s ease;">
            
            <!-- Header Section -->
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                <div style="flex: 1;">
                    <h3 style="margin: 0 0 0.5rem 0; color: #1f2937; font-size: 1.25rem; font-weight: 600;">
                        {event['title']}
                    </h3>
                    <div style="display: flex; gap: 0.75rem; flex-wrap: wrap;">
                        <span style="background: {status['bg']}; color: {status['color']}; 
                                     padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600;">
                            {status['icon']} {status['text']}
                        </span>
                        {'<span style="background: #dbeafe; color: #1d4ed8; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.75rem; font-weight: 600;">✓ Registered</span>' if is_registered else ''}
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="color: {time_color}; font-weight: 600; font-size: 0.875rem;">
                        {time_info}
                    </div>
                    <div style="color: #6b7280; font-size: 0.75rem;">
                        {event['event_date'].strftime('%B %d, %Y')}
                    </div>
                </div>
            </div>
            
            <!-- Content Section -->
            <div style="margin-bottom: 1rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                    <div>
                        <div style="color: #6b7280; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; margin-bottom: 0.25rem;">
                            📍 Location
                        </div>
                        <div style="color: #374151; font-weight: 500;">
                            {event['location'] or 'Location TBA'}
                        </div>
                    </div>
                    <div>
                        <div style="color: #6b7280; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; margin-bottom: 0.25rem;">
                            👥 Capacity
                        </div>
                        <div style="color: #374151; font-weight: 500;">
                            {capacity_info}
                        </div>
                    </div>
                    {f'''<div>
                        <div style="color: #6b7280; font-size: 0.75rem; font-weight: 600; text-transform: uppercase; margin-bottom: 0.25rem;">
                            ⏰ Registration Deadline
                        </div>
                        <div style="color: #374151; font-weight: 500;">
                            {event['registration_deadline'].strftime('%B %d, %Y') if event.get('registration_deadline') else 'No deadline'}
                        </div>
                    </div>''' if event.get('registration_deadline') else ''}
                </div>
                
                <div style="color: #6b7280; font-size: 0.875rem; line-height: 1.5;">
                    {event['description'][:150] + '...' if event['description'] and len(event['description']) > 150 else event['description'] or 'No description available'}
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)
        
        # Action buttons with enhanced styling
        col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 2, 2])
        
        with col1:
            if not is_registered and event['status'] == 'open':
                if st.button("📝 Register Now", key=f"register_{event['id']}", type="primary", use_container_width=True):
                    success, message = register_for_event(user_email, event['id'])
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)
            elif is_registered:
                st.success("✅ Registered", help="You are registered for this event")
        
        with col2:
            if is_registered and can_modify_registration(event.get('registration_deadline')):
                if st.button("❌ Cancel", key=f"cancel_{event['id']}", use_container_width=True):
                    success, message = cancel_registration(user_email, event['id'])
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)
        
        with col3:
            if st.button("👥 Participants", key=f"participants_{event['id']}", use_container_width=True):
                st.session_state[f"show_participants_{event['id']}"] = True
        
        with col4:
            if st.button("📧 Contact", key=f"contact_{event['id']}", use_container_width=True):
                st.session_state[f"contact_organizer_{event['id']}"] = True
        
        with col5:
            if st.button("ℹ️ Details", key=f"details_{event['id']}", use_container_width=True):
                st.session_state[f"show_details_{event['id']}"] = True
        
        # Handle expanded sections
        handle_event_expansions(event, user_email)

def apply_enhanced_filters(events, status_filter, date_filter, category_filter, search_term):
    """Apply enhanced filtering logic"""
    filtered_events = events.copy()
    
    # Status filter
    if status_filter != "All":
        filtered_events = [e for e in filtered_events if e['status'] == status_filter.lower()]
    
    # Date filter
    today = date.today()
    if date_filter == "Upcoming":
        filtered_events = [e for e in filtered_events if e['event_date'] >= today]
    elif date_filter == "Past":
        filtered_events = [e for e in filtered_events if e['event_date'] < today]
    elif date_filter == "This Month":
        start_month = today.replace(day=1)
        if today.month == 12:
            end_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        filtered_events = [e for e in filtered_events if start_month <= e['event_date'] <= end_month]
    elif date_filter == "Next Month":
        if today.month == 12:
            next_month = today.replace(year=today.year + 1, month=1, day=1)
            end_next_month = today.replace(year=today.year + 1, month=2, day=1) - timedelta(days=1)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
            if today.month == 11:
                end_next_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_next_month = today.replace(month=today.month + 2, day=1) - timedelta(days=1)
        filtered_events = [e for e in filtered_events if next_month <= e['event_date'] <= end_next_month]
    
    # Category filter (based on title keywords)
    if category_filter != "All":
        category_keywords = {
            "Networking": ["networking", "network", "connect", "meet"],
            "Workshop": ["workshop", "training", "hands-on", "practical"],
            "Seminar": ["seminar", "lecture", "presentation", "talk"],
            "Social": ["social", "party", "gathering", "celebration"],
            "Career": ["career", "job", "professional", "development"]
        }
        keywords = category_keywords.get(category_filter, [])
        if keywords:
            filtered_events = [
                e for e in filtered_events 
                if any(keyword.lower() in (e['title'] or '').lower() or 
                      keyword.lower() in (e['description'] or '').lower() 
                      for keyword in keywords)
            ]
    
    # Search filter
    if search_term:
        search_lower = search_term.lower()
        filtered_events = [
            e for e in filtered_events
            if (search_lower in (e['title'] or '').lower() or 
                search_lower in (e['description'] or '').lower() or
                search_lower in (e['location'] or '').lower())
        ]
    
    return filtered_events

def sort_events(events, sort_option):
    """Sort events based on selected option"""
    if sort_option == "Date (Newest)":
        return sorted(events, key=lambda x: x['event_date'], reverse=True)
    elif sort_option == "Date (Oldest)":
        return sorted(events, key=lambda x: x['event_date'])
    elif sort_option == "Title A-Z":
        return sorted(events, key=lambda x: x['title'].lower())
    elif sort_option == "Title Z-A":
        return sorted(events, key=lambda x: x['title'].lower(), reverse=True)
    elif sort_option == "Registration Count":
        return sorted(events, key=lambda x: x['registration_count'], reverse=True)
    return events

def handle_event_expansions(event, user_email):
    """Handle expanded sections for events"""
    # Show participants if requested
    if st.session_state.get(f"show_participants_{event['id']}", False):
        with st.expander(f"👥 Participants - {event['title']}", expanded=True):
            from event_management import show_event_participants
            show_event_participants(event['id'], event['title'])
            
            if st.button("Close", key=f"close_participants_{event['id']}"):
                del st.session_state[f"show_participants_{event['id']}"]
                st.rerun()
    
    # Show contact organizer if requested
    if st.session_state.get(f"contact_organizer_{event['id']}", False):
        with st.expander(f"📧 Contact Organizer - {event['title']}", expanded=True):
            from communication_system import show_contact_organizer_form
            show_contact_organizer_form(event['id'], event['title'], user_email)
            
            if st.button("Close", key=f"close_contact_{event['id']}"):
                del st.session_state[f"contact_organizer_{event['id']}"]
                st.rerun()
    
    # Show details if requested
    if st.session_state.get(f"show_details_{event['id']}", False):
        with st.expander(f"ℹ️ Event Details - {event['title']}", expanded=True):
            show_enhanced_event_details(event)
            
            if st.button("Close", key=f"close_details_{event['id']}"):
                del st.session_state[f"show_details_{event['id']}"]
                st.rerun()

def show_enhanced_event_details(event):
    """Show enhanced event details"""
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📋 Event Information")
        st.markdown(f"**Title:** {event['title']}")
        st.markdown(f"**Date:** {event['event_date'].strftime('%A, %B %d, %Y')}")
        st.markdown(f"**Location:** {event['location'] or 'TBA'}")
        st.markdown(f"**Status:** {event['status'].title()}")
        
    with col2:
        st.markdown("#### 👥 Registration Details")
        st.markdown(f"**Current Registrations:** {event['registration_count']}")
        if event.get('max_participants'):
            st.markdown(f"**Maximum Capacity:** {event['max_participants']}")
            remaining = event['max_participants'] - event['registration_count']
            st.markdown(f"**Spots Remaining:** {remaining}")
        
        if event.get('registration_deadline'):
            st.markdown(f"**Registration Deadline:** {event['registration_deadline'].strftime('%B %d, %Y')}")
    
    if event['description']:
        st.markdown("#### 📝 Description")
        st.markdown(event['description'])
    
    # Additional event metadata
    if event.get('created_at'):
        st.markdown("#### ℹ️ Additional Information")
        st.markdown(f"**Event Created:** {event['created_at'].strftime('%B %d, %Y')}")

def show_enhanced_user_management(role):
    """Enhanced user management with better presentation and user-friendliness"""
    # Clean up any lingering profile view session states
    cleanup_profile_view_states()

    # Professional header using Streamlit components
    st.markdown("# 👥 User Management")
    st.markdown("**Comprehensive user administration and management tools**")
    st.markdown("---")

    # Enhanced statistics dashboard
    show_user_statistics_dashboard()

    # Enhanced tabs with better organization
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "👀 Browse Users",
        "✏️ Manage User",
        "➕ Add New User",
        "⚙️ User Status",
        "📊 User Analytics"
    ])

    with tab1:
        show_enhanced_user_browser()

    with tab2:
        show_enhanced_user_editor()

    with tab3:
        show_enhanced_user_creator()

    with tab4:
        show_enhanced_user_status_manager()

    with tab5:
        show_enhanced_user_analytics()

def show_user_statistics_dashboard():
    """Show enhanced user statistics dashboard"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Get comprehensive statistics
        cur.execute("""
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_active THEN 1 END) as active_users,
                COUNT(CASE WHEN is_confirmed THEN 1 END) as confirmed_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
                COUNT(CASE WHEN role = 'staff' THEN 1 END) as staff_count,
                COUNT(CASE WHEN role = 'student' THEN 1 END) as student_count,
                COUNT(CASE WHEN role = 'alumni' THEN 1 END) as alumni_count,
                COUNT(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as new_users_month
            FROM users
        """)

        stats = cur.fetchone()
        conn.close()

        # Display metrics in professional cards
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.markdown(create_metric_card(
                str(stats['total_users']),
                "Total Users",
                "👥",
                "primary"
            ), unsafe_allow_html=True)

        with col2:
            active_pct = (stats['active_users'] / stats['total_users'] * 100) if stats['total_users'] > 0 else 0
            st.markdown(create_metric_card(
                f"{stats['active_users']} ({active_pct:.1f}%)",
                "Active Users",
                "🟢",
                "success"
            ), unsafe_allow_html=True)

        with col3:
            confirmed_pct = (stats['confirmed_users'] / stats['total_users'] * 100) if stats['total_users'] > 0 else 0
            st.markdown(create_metric_card(
                f"{stats['confirmed_users']} ({confirmed_pct:.1f}%)",
                "Confirmed Users",
                "✅",
                "success"
            ), unsafe_allow_html=True)

        with col4:
            st.markdown(create_metric_card(
                str(stats['new_users_month']),
                "New This Month",
                "📈",
                "primary"
            ), unsafe_allow_html=True)

        # Role distribution
        st.markdown("#### 📊 User Role Distribution")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("👑 Admins", stats['admin_count'])
        with col2:
            st.metric("👨‍🏫 Staff", stats['staff_count'])
        with col3:
            st.metric("👨‍🎓 Students", stats['student_count'])
        with col4:
            st.metric("🎓 Alumni", stats['alumni_count'])

    except Exception as e:
        st.error(f"Error loading statistics: {e}")

def show_enhanced_user_browser():
    """Enhanced user browser with advanced search and filtering"""
    st.markdown("### 🔍 Browse and Search Users")

    # Advanced search section
    st.markdown("#### 🔍 Advanced Search & Filters")
    st.markdown("---")

    # Search and filter controls
    col1, col2, col3 = st.columns(3)

    with col1:
        search_query = st.text_input(
            "🔍 Search Users",
            placeholder="Name, email, or department...",
            help="Search across name, email, and department fields"
        )

    with col2:
        role_filter = st.selectbox(
            "👤 Filter by Role",
            ["All Roles", "admin", "staff", "student", "alumni"],
            help="Filter users by their system role"
        )

    with col3:
        status_filter = st.selectbox(
            "📊 Filter by Status",
            ["All Status", "Active", "Inactive", "Confirmed", "Pending"],
            help="Filter by user account status"
        )

    # Additional filters
    col1, col2, col3 = st.columns(3)

    with col1:
        department_filter = st.selectbox(
            "🏢 Department",
            get_departments_list(),
            help="Filter by department"
        )

    with col2:
        sort_option = st.selectbox(
            "📋 Sort by",
            ["Name A-Z", "Name Z-A", "Email", "Role", "Recent", "Department"],
            help="Choose sorting order"
        )

    with col3:
        items_per_page = st.selectbox(
            "📄 Items per page",
            [10, 25, 50, 100],
            index=1,
            help="Number of users to display per page"
        )

    # Fetch and display users
    users = fetch_filtered_users(search_query, role_filter, status_filter, department_filter, sort_option)

    if users:
        display_users_with_pagination(users, items_per_page)
    else:
        st.markdown("""
        <div style="text-align: center; padding: 2rem; background: #f8fafc;
                    border-radius: 8px; border: 2px dashed #cbd5e1;">
            <h4 style="color: #64748b;">👤 No users found</h4>
            <p style="color: #94a3b8;">Try adjusting your search criteria</p>
        </div>
        """, unsafe_allow_html=True)

def show_enhanced_user_editor():
    """Enhanced user editor with user selection and editing capabilities"""
    st.markdown("### ✏️ Edit User Information")

    # User selection section
    st.markdown("#### 👤 Select User to Edit")
    st.markdown("Choose a user to view and modify their information")
    st.markdown("---")

    # User selection with search
    col1, col2 = st.columns([3, 1])

    with col1:
        user_search = st.text_input(
            "🔍 Search for user",
            placeholder="Type name or email to search...",
            help="Search for the user you want to edit"
        )

    with col2:
        if st.button("🔍 Search Users", use_container_width=True):
            if user_search:
                st.session_state['user_search_results'] = search_users_for_editing(user_search)

    # Display search results
    if 'user_search_results' in st.session_state and st.session_state['user_search_results']:
        st.markdown("#### 📋 Search Results")

        for user in st.session_state['user_search_results']:
            with st.container():
                col1, col2, col3 = st.columns([3, 2, 1])

                with col1:
                    st.markdown(f"""
                    **{user['firstname']} {user['lastname']}**

                    📧 {user['email']}

                    🏢 {user['department']} | {user['role'].title()}
                    """)

                with col2:
                    status_color = "🟢" if user['is_active'] else "🔴"
                    confirmed_status = "✅" if user['is_confirmed'] else "⏳"
                    st.markdown(f"""
                    **Status:** {status_color} {'Active' if user['is_active'] else 'Inactive'}

                    **Confirmed:** {confirmed_status} {'Yes' if user['is_confirmed'] else 'Pending'}
                    """)

                with col3:
                    if st.button("✏️ Edit", key=f"edit_user_{user['id']}", use_container_width=True):
                        st.session_state['selected_user_for_edit'] = user
                        st.rerun()

                st.markdown("---")

    # Show edit form if user is selected
    if 'selected_user_for_edit' in st.session_state:
        show_user_edit_form(st.session_state['selected_user_for_edit'])

def get_departments_list():
    """Get list of departments for filtering"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("SELECT DISTINCT department FROM users WHERE department IS NOT NULL ORDER BY department")
        departments = ["All Departments"] + [row[0] for row in cur.fetchall()]
        conn.close()
        return departments
    except:
        return ["All Departments", "Computer Science", "Engineering", "Business", "Medicine"]

def fetch_filtered_users(search_query, role_filter, status_filter, department_filter, sort_option):
    """Fetch users with applied filters"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Build query
        query = """
            SELECT id, firstname, lastname, email, role, department,
                   is_active, is_confirmed, created_at, updated_at
            FROM users
        """

        where_clauses = []
        params = []

        # Apply filters
        if search_query:
            where_clauses.append("""
                (firstname ILIKE %s OR lastname ILIKE %s OR email ILIKE %s OR department ILIKE %s)
            """)
            search_param = f"%{search_query}%"
            params.extend([search_param, search_param, search_param, search_param])

        if role_filter != "All Roles":
            where_clauses.append("role = %s")
            params.append(role_filter)

        if status_filter == "Active":
            where_clauses.append("is_active = TRUE")
        elif status_filter == "Inactive":
            where_clauses.append("is_active = FALSE")
        elif status_filter == "Confirmed":
            where_clauses.append("is_confirmed = TRUE")
        elif status_filter == "Pending":
            where_clauses.append("is_confirmed = FALSE")

        if department_filter != "All Departments":
            where_clauses.append("department = %s")
            params.append(department_filter)

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        # Apply sorting
        if sort_option == "Name A-Z":
            query += " ORDER BY firstname, lastname"
        elif sort_option == "Name Z-A":
            query += " ORDER BY firstname DESC, lastname DESC"
        elif sort_option == "Email":
            query += " ORDER BY email"
        elif sort_option == "Role":
            query += " ORDER BY role, firstname"
        elif sort_option == "Recent":
            query += " ORDER BY created_at DESC"
        elif sort_option == "Department":
            query += " ORDER BY department, firstname"

        cur.execute(query, params)
        users = cur.fetchall()
        conn.close()

        return users

    except Exception as e:
        st.error(f"Error fetching users: {e}")
        return []

def display_users_with_pagination(users, items_per_page):
    """Display users with pagination and enhanced presentation"""
    total_users = len(users)
    total_pages = (total_users + items_per_page - 1) // items_per_page

    # Initialize page number
    if 'user_page_number' not in st.session_state:
        st.session_state.user_page_number = 0

    # Results summary
    col1, col2 = st.columns([2, 1])
    with col1:
        st.markdown(f"**Found {total_users} users**")
    with col2:
        if st.button("📊 Export Results", help="Export filtered results to CSV"):
            export_users_to_csv(users)

    # Pagination controls
    if total_pages > 1:
        col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

        with col1:
            if st.button("⏮️ First", disabled=st.session_state.user_page_number <= 0):
                st.session_state.user_page_number = 0
                st.rerun()

        with col2:
            if st.button("◀️ Prev", disabled=st.session_state.user_page_number <= 0):
                st.session_state.user_page_number -= 1
                st.rerun()

        with col3:
            st.markdown(f"<div style='text-align: center; padding: 0.5rem;'>Page {st.session_state.user_page_number + 1} of {total_pages}</div>", unsafe_allow_html=True)

        with col4:
            if st.button("▶️ Next", disabled=st.session_state.user_page_number >= total_pages - 1):
                st.session_state.user_page_number += 1
                st.rerun()

        with col5:
            if st.button("⏭️ Last", disabled=st.session_state.user_page_number >= total_pages - 1):
                st.session_state.user_page_number = total_pages - 1
                st.rerun()

    # Get current page data
    start_idx = st.session_state.user_page_number * items_per_page
    end_idx = min(start_idx + items_per_page, total_users)
    page_users = users[start_idx:end_idx]

    # Display users in enhanced cards
    for user in page_users:
        display_enhanced_user_card(user)

    # Page info
    if total_pages > 1:
        st.markdown(f"<div style='text-align: center; color: #6b7280; margin-top: 1rem;'>Showing {start_idx + 1}-{end_idx} of {total_users} users</div>", unsafe_allow_html=True)

def display_enhanced_user_card(user):
    """Display enhanced user card with proper rendering"""
    # Use Streamlit native components instead of raw HTML to avoid display issues

    # Status indicators
    status_emoji = "🟢" if user['is_active'] else "🔴"
    status_text = "Active" if user['is_active'] else "Inactive"

    confirmed_emoji = "✅" if user['is_confirmed'] else "⏳"
    confirmed_text = "Confirmed" if user['is_confirmed'] else "Pending"

    # Role emoji mapping
    role_emojis = {
        'admin': '👑',
        'staff': '👨‍🏫',
        'student': '👨‍🎓',
        'alumni': '🎓'
    }
    role_emoji = role_emojis.get(user['role'], '👤')

    # Create a professional card using Streamlit components
    with st.container():
        # Create a bordered container
        st.markdown("""
        <div style="border: 1px solid #e5e7eb; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; background: white;">
        </div>
        """, unsafe_allow_html=True)

        # User information in columns
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown(f"### {user['firstname']} {user['lastname']}")
            st.markdown(f"📧 **Email:** {user['email']}")
            st.markdown(f"🏢 **Department:** {user['department'] or 'No department'}")
            st.markdown(f"👤 **ID:** {user['id']}")

            # Dates
            if user['created_at']:
                st.markdown(f"📅 **Joined:** {user['created_at'].strftime('%B %Y')}")
            if user['updated_at']:
                st.markdown(f"🔄 **Updated:** {user['updated_at'].strftime('%B %Y')}")

        with col2:
            # Status badges using Streamlit
            st.markdown(f"**Role:** {role_emoji} {user['role'].title()}")
            st.markdown(f"**Status:** {status_emoji} {status_text}")
            st.markdown(f"**Confirmation:** {confirmed_emoji} {confirmed_text}")

        st.markdown("---")

        # Action buttons (removed Profile View as it's not needed)
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("✏️ Edit User", key=f"edit_user_{user['id']}", use_container_width=True):
                st.session_state['selected_user_for_edit'] = user
                st.session_state['show_edit_form'] = True
                st.rerun()

        with col2:
            status_action = "Deactivate" if user['is_active'] else "Activate"
            if st.button(f"⚙️ {status_action}", key=f"toggle_status_{user['id']}", use_container_width=True):
                if toggle_user_status(user['id'], not user['is_active']):
                    st.success(f"User {status_action.lower()}d successfully!")
                    st.rerun()
                else:
                    st.error(f"Failed to {status_action.lower()} user")

        with col3:
            if st.button("🗑️ Delete", key=f"delete_user_{user['id']}", use_container_width=True):
                st.session_state[f"confirm_delete_{user['id']}"] = True
                st.rerun()

    # Handle expanded sections (only edit and delete confirmations now)
    handle_user_card_expansions(user)

def handle_user_card_expansions(user):
    """Handle expanded sections for user cards (edit and delete only)"""
    # Show edit form if requested
    if st.session_state.get('show_edit_form', False) and st.session_state.get('selected_user_for_edit', {}).get('id') == user['id']:
        with st.expander(f"✏️ Edit User - {user['firstname']} {user['lastname']}", expanded=True):
            show_user_edit_form(user)

    # Show delete confirmation if requested
    if st.session_state.get(f"confirm_delete_{user['id']}", False):
        with st.expander(f"🗑️ Delete User - {user['firstname']} {user['lastname']}", expanded=True):
            st.warning("⚠️ This action cannot be undone!")
            st.markdown(f"Are you sure you want to delete **{user['firstname']} {user['lastname']}** ({user['email']})?")

            col1, col2 = st.columns(2)
            with col1:
                if st.button("🗑️ Confirm Delete", key=f"confirm_delete_final_{user['id']}", type="primary"):
                    if delete_user(user['id']):
                        st.success("User deleted successfully!")
                        del st.session_state[f"confirm_delete_{user['id']}"]
                        st.rerun()
                    else:
                        st.error("Failed to delete user")

            with col2:
                if st.button("Cancel", key=f"cancel_delete_{user['id']}"):
                    del st.session_state[f"confirm_delete_{user['id']}"]
                    st.rerun()

def search_users_for_editing(search_query):
    """Search users for editing purposes"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cur.execute("""
            SELECT id, firstname, lastname, email, role, department, is_active, is_confirmed
            FROM users
            WHERE firstname ILIKE %s OR lastname ILIKE %s OR email ILIKE %s
            ORDER BY firstname, lastname
            LIMIT 10
        """, (f"%{search_query}%", f"%{search_query}%", f"%{search_query}%"))

        users = cur.fetchall()
        conn.close()

        return users

    except Exception as e:
        st.error(f"Error searching users: {e}")
        return []

def show_user_edit_form(user):
    """Show user edit form"""
    with st.form(f"edit_user_form_{user['id']}"):
        col1, col2 = st.columns(2)

        with col1:
            firstname = st.text_input("First Name", value=user['firstname'])
            lastname = st.text_input("Last Name", value=user['lastname'])
            email = st.text_input("Email", value=user['email'])

        with col2:
            role = st.selectbox(
                "Role",
                ["admin", "staff", "student", "alumni"],
                index=["admin", "staff", "student", "alumni"].index(user['role'])
            )
            department = st.text_input("Department", value=user.get('department', '') or '')

            # Status controls
            is_active = st.checkbox("Active", value=user['is_active'])
            is_confirmed = st.checkbox("Confirmed", value=user['is_confirmed'])

        # Interests handling
        interests_str = ""
        if user.get('interests'):
            if isinstance(user['interests'], list):
                interests_str = ", ".join(user['interests'])
            else:
                interests_str = str(user['interests'])

        interests = st.text_area("Interests (comma-separated)", value=interests_str)

        col1, col2 = st.columns(2)
        with col1:
            if st.form_submit_button("💾 Save Changes", use_container_width=True):
                if update_user_details(user['id'], firstname, lastname, email, role, department, interests, is_active, is_confirmed):
                    st.success("User updated successfully!")
                    del st.session_state['show_edit_form']
                    if 'selected_user_for_edit' in st.session_state:
                        del st.session_state['selected_user_for_edit']
                    st.rerun()
                else:
                    st.error("Failed to update user")

        with col2:
            if st.form_submit_button("❌ Cancel", use_container_width=True):
                del st.session_state['show_edit_form']
                if 'selected_user_for_edit' in st.session_state:
                    del st.session_state['selected_user_for_edit']
                st.rerun()

def show_enhanced_user_creator():
    """Enhanced user creation interface"""
    st.markdown("### ➕ Add New User")

    st.markdown("#### 👤 Create New User Account")
    st.markdown("Add a new user to the alumni management system")
    st.markdown("---")

    with st.form("enhanced_add_user_form"):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 📝 Basic Information")
            firstname = st.text_input("First Name *", placeholder="Enter first name")
            lastname = st.text_input("Last Name *", placeholder="Enter last name")
            email = st.text_input("Email Address *", placeholder="Enter email address")
            password = st.text_input("Password *", type="password", placeholder="Enter password")

        with col2:
            st.markdown("#### 🏢 Role & Department")
            role = st.selectbox("Role *", ["student", "alumni", "staff", "admin"])
            department = st.selectbox("Department", [
                "", "Computer Science", "Engineering", "Business", "Medicine",
                "Arts & Sciences", "Education", "Law", "Other"
            ])

            # Additional fields based on role
            if role == "alumni":
                graduation_year = st.number_input("Graduation Year", min_value=1950, max_value=2030, value=2023)

            current_position = st.text_input("Current Position", placeholder="Optional")

        interests = st.text_area("Interests", placeholder="Enter interests (comma-separated)")

        # Status settings
        col1, col2 = st.columns(2)
        with col1:
            is_active = st.checkbox("Active Account", value=True)
        with col2:
            is_confirmed = st.checkbox("Pre-confirmed Account", value=False)

        submit = st.form_submit_button("➕ Create User", use_container_width=True, type="primary")

        if submit:
            if not all([firstname, lastname, email, password]):
                st.error("All required fields (*) must be filled")
            else:
                # Create user
                success = create_new_user(
                    firstname, lastname, email, password, role, department,
                    interests, current_position if 'current_position' in locals() else None,
                    graduation_year if 'graduation_year' in locals() else None,
                    is_active, is_confirmed
                )

                if success:
                    st.success(f"User {firstname} {lastname} created successfully!")
                    st.balloons()
                    # Clear form by rerunning the page
                    st.rerun()
                else:
                    st.error("Failed to create user")

def show_enhanced_user_status_manager():
    """Enhanced user status management"""
    st.markdown("### ⚙️ User Status Management")

    # Quick actions section
    st.markdown("#### ⚙️ Quick Status Actions")
    st.markdown("Manage user account status and confirmations")
    st.markdown("---")

    # Pending confirmations
    show_pending_confirmations()

    # Bulk status operations
    show_bulk_status_operations()

def cleanup_profile_view_states():
    """Clean up any lingering profile view session states"""
    keys_to_remove = []
    for key in st.session_state.keys():
        if key.startswith('show_profile_') or key.startswith('view_profile_'):
            keys_to_remove.append(key)

    for key in keys_to_remove:
        del st.session_state[key]

def show_enhanced_user_analytics():
    """Enhanced user analytics and reporting"""
    st.markdown("### 📊 User Analytics & Reports")

    # Analytics dashboard
    show_detailed_user_analytics()

# Helper functions
def toggle_user_status(user_id, new_status):
    """Toggle user active status"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("UPDATE users SET is_active = %s WHERE id = %s", (new_status, user_id))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error updating user status: {e}")
        return False

def delete_user(user_id):
    """Delete user from database"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("DELETE FROM users WHERE id = %s", (user_id,))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error deleting user: {e}")
        return False

def update_user_details(user_id, firstname, lastname, email, role, department, interests, is_active, is_confirmed):
    """Update user details"""
    try:
        conn = get_connection()
        cur = conn.cursor()

        # Convert interests to list
        interests_list = [i.strip() for i in interests.split(',') if i.strip()] if interests else []

        cur.execute("""
            UPDATE users
            SET firstname = %s, lastname = %s, email = %s, role = %s,
                department = %s, interests = %s, is_active = %s, is_confirmed = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (firstname, lastname, email, role, department, interests_list, is_active, is_confirmed, user_id))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error updating user: {e}")
        return False

def create_new_user(firstname, lastname, email, password, role, department, interests, current_position, graduation_year, is_active, is_confirmed):
    """Create new user"""
    try:
        import bcrypt

        # Hash password
        hashed_password = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()

        # Convert interests to list
        interests_list = [i.strip() for i in interests.split(',') if i.strip()] if interests else []

        conn = get_connection()
        cur = conn.cursor()

        cur.execute("""
            INSERT INTO users (firstname, lastname, email, password_hash, role, department,
                             interests, current_position, graduation_year, is_active, is_confirmed, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
        """, (firstname, lastname, email, hashed_password, role, department,
              interests_list, current_position, graduation_year, is_active, is_confirmed))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error creating user: {e}")
        return False

def export_users_to_csv(users):
    """Export users to CSV"""
    import pandas as pd

    df = pd.DataFrame(users)
    csv = df.to_csv(index=False)

    st.download_button(
        label="📥 Download CSV",
        data=csv,
        file_name=f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
        mime="text/csv",
        use_container_width=True
    )

def show_pending_confirmations():
    """Show pending user confirmations"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cur.execute("""
            SELECT id, firstname, lastname, email, role, created_at
            FROM users
            WHERE is_confirmed = FALSE
            ORDER BY created_at DESC
        """)

        pending_users = cur.fetchall()
        conn.close()

        if pending_users:
            st.markdown("#### ⏳ Pending Confirmations")

            for user in pending_users:
                col1, col2, col3 = st.columns([3, 2, 1])

                with col1:
                    st.markdown(f"**{user['firstname']} {user['lastname']}**")
                    st.markdown(f"📧 {user['email']}")

                with col2:
                    st.markdown(f"**Role:** {user['role'].title()}")
                    st.markdown(f"**Registered:** {user['created_at'].strftime('%B %d, %Y')}")

                with col3:
                    if st.button("✅ Confirm", key=f"confirm_pending_{user['id']}", use_container_width=True):
                        if confirm_user_account(user['id']):
                            st.success("User confirmed!")
                            st.rerun()

                st.markdown("---")
        else:
            st.info("No pending confirmations")

    except Exception as e:
        st.error(f"Error loading pending confirmations: {e}")

def show_bulk_status_operations():
    """Show bulk status operations"""
    st.markdown("#### 🔧 Bulk Operations")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("✅ Confirm All Pending", use_container_width=True):
            if confirm_all_pending_users():
                st.success("All pending users confirmed!")
                st.rerun()

    with col2:
        if st.button("🔄 Activate All Users", use_container_width=True):
            if activate_all_users():
                st.success("All users activated!")
                st.rerun()

    with col3:
        if st.button("📊 Generate Report", use_container_width=True):
            generate_user_report()

def show_detailed_user_analytics():
    """Show detailed user analytics"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Registration trends
        cur.execute("""
            SELECT
                DATE_TRUNC('month', created_at) as month,
                COUNT(*) as registrations
            FROM users
            WHERE created_at >= CURRENT_DATE - INTERVAL '12 months'
            GROUP BY DATE_TRUNC('month', created_at)
            ORDER BY month
        """)

        monthly_data = cur.fetchall()

        if monthly_data:
            st.markdown("#### 📈 Registration Trends (Last 12 Months)")

            import pandas as pd
            df = pd.DataFrame(monthly_data)
            df['month'] = pd.to_datetime(df['month'])
            df['month_str'] = df['month'].dt.strftime('%B %Y')

            st.line_chart(df.set_index('month_str')['registrations'])

        # Department distribution
        cur.execute("""
            SELECT department, COUNT(*) as count
            FROM users
            WHERE department IS NOT NULL
            GROUP BY department
            ORDER BY count DESC
        """)

        dept_data = cur.fetchall()

        if dept_data:
            st.markdown("#### 🏢 Department Distribution")

            df_dept = pd.DataFrame(dept_data)
            st.bar_chart(df_dept.set_index('department')['count'])

        conn.close()

    except Exception as e:
        st.error(f"Error loading analytics: {e}")

def confirm_user_account(user_id):
    """Confirm a user account"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            UPDATE users
            SET is_confirmed = TRUE, confirmed_at = CURRENT_TIMESTAMP,
                confirmed_by = %s
            WHERE id = %s
        """, (st.session_state.get('user_email', 'admin'), user_id))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error confirming user: {e}")
        return False

def confirm_all_pending_users():
    """Confirm all pending users"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("""
            UPDATE users
            SET is_confirmed = TRUE, confirmed_at = CURRENT_TIMESTAMP,
                confirmed_by = %s
            WHERE is_confirmed = FALSE
        """, (st.session_state.get('user_email', 'admin'),))
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error confirming all users: {e}")
        return False

def activate_all_users():
    """Activate all users"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        cur.execute("UPDATE users SET is_active = TRUE")
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        st.error(f"Error activating all users: {e}")
        return False

def generate_user_report():
    """Generate comprehensive user report"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        cur.execute("""
            SELECT firstname, lastname, email, role, department,
                   is_active, is_confirmed, created_at, updated_at
            FROM users
            ORDER BY created_at DESC
        """)

        users = cur.fetchall()
        conn.close()

        if users:
            import pandas as pd
            df = pd.DataFrame(users)

            # Format dates
            df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d')
            df['updated_at'] = pd.to_datetime(df['updated_at']).dt.strftime('%Y-%m-%d')

            csv = df.to_csv(index=False)

            st.download_button(
                label="📊 Download User Report",
                data=csv,
                file_name=f"user_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
                use_container_width=True
            )

    except Exception as e:
        st.error(f"Error generating report: {e}")
