#!/usr/bin/env python3
"""
Complete Authentication and User Management System
Includes password reset, user registration, and profile management
"""

import streamlit as st
import bcrypt
import secrets
import string
from datetime import datetime, timedelta
from db import get_connection
import psycopg2.extras
import re
from ui_components import create_professional_card, show_alert, create_action_buttons

def generate_reset_token():
    """Generate a secure reset token"""
    return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    return True, "Password is valid"

def create_password_reset_table():
    """Create password reset tokens table if it doesn't exist"""
    try:
        conn = get_connection()
        cur = conn.cursor()
        
        cur.execute("""
            CREATE TABLE IF NOT EXISTS password_reset_tokens (
                id SERIAL PRIMARY KEY,
                user_id INTEGER REFERENCES users(id),
                token VARCHAR(255) UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                used BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Error creating password reset table: {e}")
        return False

def request_password_reset(email):
    """Request password reset for user"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Check if user exists
        cur.execute("SELECT id, firstname, lastname FROM users WHERE email = %s", (email,))
        user = cur.fetchone()
        
        if not user:
            return False, "No account found with this email address"
        
        # Generate reset token
        token = generate_reset_token()
        expires_at = datetime.now() + timedelta(hours=24)
        
        # Store reset token
        cur.execute("""
            INSERT INTO password_reset_tokens (user_id, token, expires_at)
            VALUES (%s, %s, %s)
        """, (user['id'], token, expires_at))
        
        conn.commit()
        conn.close()
        
        # In a real application, you would send an email here
        # For demo purposes, we'll show the token
        return True, f"Password reset requested. Reset token: {token} (expires in 24 hours)"
        
    except Exception as e:
        return False, f"Error requesting password reset: {e}"

def reset_password_with_token(token, new_password):
    """Reset password using token"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Validate token
        cur.execute("""
            SELECT prt.user_id, prt.expires_at, prt.used, u.email
            FROM password_reset_tokens prt
            JOIN users u ON prt.user_id = u.id
            WHERE prt.token = %s
        """, (token,))
        
        reset_data = cur.fetchone()
        
        if not reset_data:
            return False, "Invalid reset token"
        
        if reset_data['used']:
            return False, "Reset token has already been used"
        
        if reset_data['expires_at'] < datetime.now():
            return False, "Reset token has expired"
        
        # Validate new password
        is_valid, message = validate_password(new_password)
        if not is_valid:
            return False, message
        
        # Hash new password
        hashed_password = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt()).decode()
        
        # Update password
        cur.execute("""
            UPDATE users SET password_hash = %s WHERE id = %s
        """, (hashed_password, reset_data['user_id']))
        
        # Mark token as used
        cur.execute("""
            UPDATE password_reset_tokens SET used = TRUE WHERE token = %s
        """, (token,))
        
        conn.commit()
        conn.close()
        
        return True, "Password reset successfully"
        
    except Exception as e:
        return False, f"Error resetting password: {e}"

def register_new_user(firstname, lastname, email, password, department, interests, role="student"):
    """Register a new user"""
    try:
        # Validate inputs
        if not all([firstname, lastname, email, password, department]):
            return False, "All fields are required"
        
        if not validate_email(email):
            return False, "Invalid email format"
        
        is_valid, message = validate_password(password)
        if not is_valid:
            return False, message
        
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Check if email already exists
        cur.execute("SELECT id FROM users WHERE email = %s", (email,))
        if cur.fetchone():
            return False, "Email already registered"
        
        # Hash password
        hashed_password = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
        
        # Insert new user
        cur.execute("""
            INSERT INTO users (firstname, lastname, email, password_hash, role, department, interests, 
                             is_active, is_confirmed, created_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, TRUE, FALSE, CURRENT_TIMESTAMP)
            RETURNING id
        """, (firstname, lastname, email, hashed_password, role, department, interests))
        
        user_id = cur.fetchone()['id']
        
        conn.commit()
        conn.close()
        
        return True, f"User registered successfully with ID {user_id}. Account pending admin confirmation."
        
    except Exception as e:
        return False, f"Error registering user: {e}"

def get_user_profile(email):
    """Get complete user profile information"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cur.execute("""
            SELECT 
                u.*,
                COUNT(DISTINCT ep.event_id) as events_registered,
                COUNT(DISTINCT c1.id) as messages_sent,
                COUNT(DISTINCT c2.id) as messages_received,
                COUNT(DISTINCT f.id) as feedback_given
            FROM users u
            LEFT JOIN event_participants ep ON u.id = ep.user_id
            LEFT JOIN chats c1 ON u.id = c1.sender_id
            LEFT JOIN chats c2 ON u.id = c2.receiver_id
            LEFT JOIN feedback f ON u.id = f.user_id
            WHERE u.email = %s
            GROUP BY u.id
        """, (email,))
        
        profile = cur.fetchone()
        conn.close()
        
        return profile
        
    except Exception as e:
        print(f"Error getting user profile: {e}")
        return None

def show_password_reset_form():
    """Show password reset form"""
    st.markdown("### 🔑 Password Reset")
    
    # Create password reset table if it doesn't exist
    create_password_reset_table()
    
    tab1, tab2 = st.tabs(["Request Reset", "Reset with Token"])
    
    with tab1:
        st.markdown("#### Request Password Reset")
        with st.form("password_reset_request"):
            email = st.text_input("Email Address", placeholder="Enter your email")
            submit = st.form_submit_button("Send Reset Link")
            
            if submit:
                if email:
                    success, message = request_password_reset(email)
                    if success:
                        st.success(message)
                        st.info("💡 In a production environment, you would receive an email with the reset link.")
                    else:
                        st.error(message)
                else:
                    st.error("Please enter your email address")
    
    with tab2:
        st.markdown("#### Reset Password with Token")
        with st.form("password_reset_confirm"):
            token = st.text_input("Reset Token", placeholder="Enter the reset token")
            new_password = st.text_input("New Password", type="password", placeholder="Enter new password")
            confirm_password = st.text_input("Confirm Password", type="password", placeholder="Confirm new password")
            submit = st.form_submit_button("Reset Password")
            
            if submit:
                if not all([token, new_password, confirm_password]):
                    st.error("All fields are required")
                elif new_password != confirm_password:
                    st.error("Passwords do not match")
                else:
                    success, message = reset_password_with_token(token, new_password)
                    if success:
                        st.success(message)
                        st.balloons()
                    else:
                        st.error(message)

def show_user_registration_form():
    """Show user registration form"""
    st.markdown("### 📝 New User Registration")
    
    with st.form("user_registration"):
        col1, col2 = st.columns(2)
        
        with col1:
            firstname = st.text_input("First Name *", placeholder="Enter first name")
            lastname = st.text_input("Last Name *", placeholder="Enter last name")
            email = st.text_input("Email Address *", placeholder="Enter email address")
        
        with col2:
            password = st.text_input("Password *", type="password", placeholder="Enter password")
            confirm_password = st.text_input("Confirm Password *", type="password", placeholder="Confirm password")
            department = st.selectbox("Department *", [
                "", "Computer Science", "Engineering", "Business", "Medicine", 
                "Arts & Sciences", "Education", "Law", "Other"
            ])
        
        role = st.selectbox("Role", ["student", "alumni", "staff"], index=0)
        
        interests = st.text_area(
            "Interests", 
            placeholder="Enter your interests (comma-separated)",
            help="e.g., AI, Machine Learning, Software Development, Business"
        )
        
        # Terms and conditions
        agree_terms = st.checkbox("I agree to the terms and conditions")
        
        submit = st.form_submit_button("Register Account", use_container_width=True)
        
        if submit:
            if not agree_terms:
                st.error("You must agree to the terms and conditions")
            elif not all([firstname, lastname, email, password, confirm_password, department]):
                st.error("All required fields (*) must be filled")
            elif password != confirm_password:
                st.error("Passwords do not match")
            else:
                # Convert interests to list
                interests_list = [i.strip() for i in interests.split(',') if i.strip()] if interests else []
                
                success, message = register_new_user(
                    firstname, lastname, email, password, department, interests_list, role
                )
                
                if success:
                    st.success(message)
                    st.balloons()
                    st.info("You can now log in with your credentials once an admin confirms your account.")
                else:
                    st.error(message)

def show_user_profile(email):
    """Show detailed user profile"""
    profile = get_user_profile(email)
    
    if not profile:
        st.error("Profile not found")
        return
    
    st.markdown(f"### 👤 Profile: {profile['firstname']} {profile['lastname']}")
    
    # Profile header
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.markdown(f"""
        **Name:** {profile['firstname']} {profile['lastname']}
        **Email:** {profile['email']}
        **Department:** {profile['department']}
        **Role:** {profile['role'].title()}
        """)
    
    with col2:
        status_color = "🟢" if profile['is_active'] else "🔴"
        confirmed_status = "✅" if profile['is_confirmed'] else "⏳"
        st.markdown(f"""
        **Status:** {status_color} {'Active' if profile['is_active'] else 'Inactive'}
        **Confirmed:** {confirmed_status} {'Yes' if profile['is_confirmed'] else 'Pending'}
        """)
    
    with col3:
        st.markdown(f"""
        **Joined:** {profile['created_at'].strftime('%B %Y') if profile['created_at'] else 'Unknown'}
        **Last Updated:** {profile['updated_at'].strftime('%B %d, %Y') if profile['updated_at'] else 'Never'}
        """)
    
    # Activity metrics
    st.markdown("#### 📊 Activity Summary")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Events Registered", profile['events_registered'])
    with col2:
        st.metric("Messages Sent", profile['messages_sent'])
    with col3:
        st.metric("Messages Received", profile['messages_received'])
    with col4:
        st.metric("Feedback Given", profile['feedback_given'])
    
    # Interests
    if profile['interests']:
        st.markdown("#### 🎯 Interests")
        interests = profile['interests'] if isinstance(profile['interests'], list) else [profile['interests']]
        for interest in interests:
            st.badge(interest)
    
    # Additional information
    if profile['confirmed_by']:
        st.markdown("#### ✅ Confirmation Details")
        st.info(f"Account confirmed by {profile['confirmed_by']} on {profile['confirmed_at'].strftime('%B %d, %Y') if profile['confirmed_at'] else 'Unknown date'}")
    
    return profile
