#!/usr/bin/env python3
"""
Script to test analytics and reporting functionality
"""

from db import get_connection
import psycopg2.extras

def test_analytics_functionality():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print("Testing analytics and reporting...")
        
        # User Statistics
        print("\n=== USER STATISTICS ===")
        
        # Total users by role
        cur.execute("""
            SELECT role, COUNT(*) as count
            FROM users
            GROUP BY role
            ORDER BY count DESC
        """)
        user_roles = cur.fetchall()
        print("Users by role:")
        for role in user_roles:
            print(f"  {role['role']}: {role['count']} users")
        
        # Active vs Inactive users
        cur.execute("""
            SELECT 
                SUM(CASE WHEN is_active = TRUE THEN 1 ELSE 0 END) as active_users,
                SUM(CASE WHEN is_active = FALSE THEN 1 ELSE 0 END) as inactive_users,
                COUNT(*) as total_users
            FROM users
        """)
        user_status = cur.fetchone()
        print(f"\nUser Status:")
        print(f"  Active: {user_status['active_users']}")
        print(f"  Inactive: {user_status['inactive_users']}")
        print(f"  Total: {user_status['total_users']}")
        
        # Confirmed vs Unconfirmed users
        cur.execute("""
            SELECT 
                SUM(CASE WHEN is_confirmed = TRUE THEN 1 ELSE 0 END) as confirmed_users,
                SUM(CASE WHEN is_confirmed = FALSE THEN 1 ELSE 0 END) as unconfirmed_users
            FROM users
        """)
        confirmation_status = cur.fetchone()
        print(f"\nConfirmation Status:")
        print(f"  Confirmed: {confirmation_status['confirmed_users']}")
        print(f"  Unconfirmed: {confirmation_status['unconfirmed_users']}")
        
        # Chat Statistics
        print("\n=== CHAT STATISTICS ===")
        
        cur.execute("""
            SELECT 
                COUNT(*) as total_messages,
                COUNT(DISTINCT sender_id) as active_senders,
                COUNT(DISTINCT receiver_id) as message_recipients
            FROM chats
        """)
        chat_stats = cur.fetchone()
        print(f"Total messages: {chat_stats['total_messages']}")
        print(f"Active senders: {chat_stats['active_senders']}")
        print(f"Message recipients: {chat_stats['message_recipients']}")
        
        # Unread messages
        cur.execute("""
            SELECT COUNT(*) as unread_messages
            FROM chats
            WHERE is_read = FALSE
        """)
        unread_stats = cur.fetchone()
        print(f"Unread messages: {unread_stats['unread_messages']}")
        
        # Most active chat users
        cur.execute("""
            SELECT u.email, COUNT(c.id) as message_count
            FROM users u
            JOIN chats c ON u.id = c.sender_id
            GROUP BY u.id, u.email
            ORDER BY message_count DESC
            LIMIT 3
        """)
        active_chatters = cur.fetchall()
        print("\nMost active chat users:")
        for user in active_chatters:
            print(f"  {user['email']}: {user['message_count']} messages")
        
        # Event Statistics
        print("\n=== EVENT STATISTICS ===")
        
        cur.execute("""
            SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_date >= CURRENT_DATE THEN 1 END) as upcoming_events,
                COUNT(CASE WHEN event_date < CURRENT_DATE THEN 1 END) as past_events
            FROM events
        """)
        event_stats = cur.fetchone()
        print(f"Total events: {event_stats['total_events']}")
        print(f"Upcoming events: {event_stats['upcoming_events']}")
        print(f"Past events: {event_stats['past_events']}")
        
        # Event participation
        cur.execute("""
            SELECT 
                COUNT(*) as total_registrations,
                COUNT(DISTINCT user_id) as unique_participants,
                COUNT(DISTINCT event_id) as events_with_participants
            FROM event_participants
        """)
        participation_stats = cur.fetchone()
        print(f"Total registrations: {participation_stats['total_registrations']}")
        print(f"Unique participants: {participation_stats['unique_participants']}")
        print(f"Events with participants: {participation_stats['events_with_participants']}")
        
        # Feedback Statistics
        print("\n=== FEEDBACK STATISTICS ===")
        
        cur.execute("""
            SELECT 
                COUNT(*) as total_feedback,
                AVG(rating) as average_rating,
                COUNT(DISTINCT user_id) as users_who_gave_feedback
            FROM feedback
        """)
        feedback_stats = cur.fetchone()
        print(f"Total feedback: {feedback_stats['total_feedback']}")
        print(f"Average rating: {feedback_stats['average_rating']:.2f}" if feedback_stats['average_rating'] else "No ratings")
        print(f"Users who gave feedback: {feedback_stats['users_who_gave_feedback']}")
        
        # Feedback by type
        cur.execute("""
            SELECT feedback_type, COUNT(*) as count
            FROM feedback
            GROUP BY feedback_type
            ORDER BY count DESC
        """)
        feedback_types = cur.fetchall()
        print("\nFeedback by type:")
        for feedback_type in feedback_types:
            print(f"  {feedback_type['feedback_type']}: {feedback_type['count']}")
        
        # Recommendations Statistics
        print("\n=== RECOMMENDATIONS STATISTICS ===")
        
        cur.execute("""
            SELECT 
                COUNT(*) as total_recommendations,
                COUNT(DISTINCT user_id) as users_with_recommendations
            FROM recommendations
        """)
        rec_stats = cur.fetchone()
        print(f"Total recommendations: {rec_stats['total_recommendations']}")
        print(f"Users with recommendations: {rec_stats['users_with_recommendations']}")
        
        # Recent activity summary
        print("\n=== RECENT ACTIVITY SUMMARY ===")
        
        # Recent messages (last 7 days)
        cur.execute("""
            SELECT COUNT(*) as recent_messages
            FROM chats
            WHERE sent_at >= CURRENT_DATE - INTERVAL '7 days'
        """)
        recent_messages = cur.fetchone()['recent_messages']
        print(f"Messages in last 7 days: {recent_messages}")
        
        # Recent feedback (last 30 days)
        cur.execute("""
            SELECT COUNT(*) as recent_feedback
            FROM feedback
            WHERE submitted_at >= CURRENT_DATE - INTERVAL '30 days'
        """)
        recent_feedback = cur.fetchone()['recent_feedback']
        print(f"Feedback in last 30 days: {recent_feedback}")
        
        # Recent event registrations (last 30 days)
        cur.execute("""
            SELECT COUNT(*) as recent_registrations
            FROM event_participants
            WHERE registered_at >= CURRENT_DATE - INTERVAL '30 days'
        """)
        recent_registrations = cur.fetchone()['recent_registrations']
        print(f"Event registrations in last 30 days: {recent_registrations}")
        
        conn.close()
        print("\nAnalytics and reporting test completed successfully!")
        
    except Exception as e:
        print(f"Error testing analytics: {e}")

if __name__ == "__main__":
    test_analytics_functionality()
