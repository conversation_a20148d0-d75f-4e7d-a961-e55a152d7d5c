#!/usr/bin/env python3
"""
Test script to verify presentation fixes
Tests form clearing and HTML display issues
"""

import subprocess
import sys
from datetime import datetime

def check_syntax():
    """Check syntax of all files"""
    print("🔍 Checking Syntax...")
    
    files_to_check = [
        'enhanced_presentation.py',
        'app.py'
    ]
    
    for file in files_to_check:
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', file], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ {file} - Syntax OK")
            else:
                print(f"  ❌ {file} - Syntax Error: {result.stderr}")
                return False
        except FileNotFoundError:
            print(f"  ⚠️ {file} - File not found")
    
    return True

def test_enhanced_presentation_fixes():
    """Test enhanced presentation fixes"""
    print("\n🎨 Testing Enhanced Presentation Fixes...")
    
    try:
        from enhanced_presentation import (
            display_enhanced_user_card,
            show_enhanced_user_management,
            show_enhanced_user_browser,
            show_enhanced_user_creator
        )
        print("  ✅ Enhanced presentation functions imported successfully")
        
        # Test that functions exist and are callable
        test_functions = [
            'display_enhanced_user_card',
            'show_enhanced_user_management', 
            'show_enhanced_user_browser',
            'show_enhanced_user_creator'
        ]
        
        for func_name in test_functions:
            try:
                func = globals().get(func_name) or locals().get(func_name)
                if callable(func):
                    print(f"  ✅ {func_name} is callable")
                else:
                    print(f"  ⚠️ {func_name} not callable")
            except:
                print(f"  ⚠️ Could not test {func_name}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Enhanced presentation import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Enhanced presentation test error: {e}")
        return False

def test_form_clearing_implementation():
    """Test form clearing implementation"""
    print("\n📝 Testing Form Clearing Implementation...")
    
    try:
        # Check main app for form clearing patterns
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        form_clearing_patterns = [
            'st.rerun()',
            'for key in st.session_state.keys():',
            'del st.session_state[key]'
        ]
        
        found_patterns = 0
        for pattern in form_clearing_patterns:
            if pattern in app_content:
                found_patterns += 1
                print(f"  ✅ Found form clearing pattern: {pattern}")
            else:
                print(f"  ⚠️ Missing pattern: {pattern}")
        
        # Check enhanced presentation for form clearing
        with open('enhanced_presentation.py', 'r', encoding='utf-8') as f:
            enhanced_content = f.read()
        
        if 'st.rerun()' in enhanced_content:
            found_patterns += 1
            print("  ✅ Enhanced presentation has form clearing")
        else:
            print("  ⚠️ Enhanced presentation missing form clearing")
        
        success_rate = (found_patterns / (len(form_clearing_patterns) + 1)) * 100
        print(f"  📊 Form clearing implementation: {found_patterns}/{len(form_clearing_patterns) + 1} patterns found ({success_rate:.1f}%)")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"  ❌ Form clearing test error: {e}")
        return False

def test_html_display_fixes():
    """Test HTML display fixes"""
    print("\n🌐 Testing HTML Display Fixes...")
    
    try:
        # Check that complex HTML has been simplified
        with open('enhanced_presentation.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count complex HTML patterns that might cause display issues
        problematic_patterns = [
            'linear-gradient',
            'rgba(',
            'flex-direction',
            'justify-content'
        ]
        
        pattern_count = 0
        for pattern in problematic_patterns:
            count = content.count(pattern)
            pattern_count += count
            if count > 0:
                print(f"  ⚠️ Found {count} instances of {pattern}")
        
        # Check for simplified alternatives
        simplified_patterns = [
            'st.markdown("###',
            'st.markdown("####',
            'st.markdown("---")',
            'st.columns('
        ]
        
        simplified_count = 0
        for pattern in simplified_patterns:
            count = content.count(pattern)
            simplified_count += count
            if count > 0:
                print(f"  ✅ Found {count} instances of simplified {pattern}")
        
        if simplified_count > pattern_count:
            print("  ✅ More simplified patterns than complex HTML")
            return True
        else:
            print(f"  ⚠️ Still has complex HTML patterns: {pattern_count} complex vs {simplified_count} simplified")
            return False
        
    except Exception as e:
        print(f"  ❌ HTML display test error: {e}")
        return False

def test_user_card_display():
    """Test user card display implementation"""
    print("\n👤 Testing User Card Display...")
    
    try:
        # Check that user card uses Streamlit components instead of raw HTML
        with open('enhanced_presentation.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the display_enhanced_user_card function
        if 'def display_enhanced_user_card' in content:
            print("  ✅ User card function found")
            
            # Check for Streamlit components usage
            streamlit_components = [
                'st.container()',
                'st.columns(',
                'st.markdown("###',
                'st.markdown("**'
            ]
            
            components_found = 0
            for component in streamlit_components:
                if component in content:
                    components_found += 1
                    print(f"  ✅ Using Streamlit component: {component}")
            
            if components_found >= 3:
                print("  ✅ User card uses Streamlit components properly")
                return True
            else:
                print(f"  ⚠️ User card may still have HTML issues: {components_found}/{len(streamlit_components)} components found")
                return False
        else:
            print("  ❌ User card function not found")
            return False
        
    except Exception as e:
        print(f"  ❌ User card test error: {e}")
        return False

def run_presentation_fixes_test():
    """Run comprehensive test of presentation fixes"""
    print("🚀 PRESENTATION FIXES VERIFICATION TEST")
    print("=" * 60)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Syntax Check", check_syntax),
        ("Enhanced Presentation Fixes", test_enhanced_presentation_fixes),
        ("Form Clearing Implementation", test_form_clearing_implementation),
        ("HTML Display Fixes", test_html_display_fixes),
        ("User Card Display", test_user_card_display)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_function():
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST SUMMARY: {passed_tests}/{total_tests} tests passed ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Presentation fixes are working correctly!")
        print("\n🌟 FIXES VERIFIED:")
        print("  ✅ Form clearing after successful submission")
        print("  ✅ HTML display issues resolved")
        print("  ✅ User cards display properly")
        print("  ✅ Enhanced presentation components working")
        print("  ✅ Simplified UI components for better compatibility")
        
    elif passed_tests >= total_tests * 0.8:
        print("✅ Most tests passed! Presentation fixes are largely working.")
        print("\n🎯 IMPROVEMENTS VERIFIED:")
        print("  ✅ Form data clearing implemented")
        print("  ✅ HTML display issues addressed")
        print("  ✅ User interface improvements working")
        
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_presentation_fixes_test()
    sys.exit(0 if success else 1)
