#!/usr/bin/env python3
"""
Advanced Admin Management System
Includes registration removal, user management, and administrative controls
"""

import streamlit as st
from datetime import datetime
from db import get_connection
import psycopg2.extras
from ui_components import create_professional_card, show_alert, create_action_buttons
import pandas as pd

def remove_user_registration(event_id, user_id, admin_email, reason=""):
    """Remove a user's registration from an event"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get registration details for logging
        cur.execute("""
            SELECT 
                u.firstname, u.lastname, u.email,
                e.title as event_title,
                ep.registered_at
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            JOIN events e ON ep.event_id = e.id
            WHERE ep.event_id = %s AND ep.user_id = %s
        """, (event_id, user_id))
        
        registration = cur.fetchone()
        
        if not registration:
            return False, "Registration not found"
        
        # Remove the registration
        cur.execute("""
            DELETE FROM event_participants 
            WHERE event_id = %s AND user_id = %s
        """, (event_id, user_id))
        
        # Log the removal (create audit log table if needed)
        try:
            cur.execute("""
                CREATE TABLE IF NOT EXISTS registration_audit_log (
                    id SERIAL PRIMARY KEY,
                    event_id INTEGER,
                    user_id INTEGER,
                    user_email VARCHAR(255),
                    event_title VARCHAR(255),
                    action VARCHAR(50),
                    admin_email VARCHAR(255),
                    reason TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            cur.execute("""
                INSERT INTO registration_audit_log 
                (event_id, user_id, user_email, event_title, action, admin_email, reason)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
            """, (event_id, user_id, registration['email'], registration['event_title'], 
                  'REMOVED', admin_email, reason))
        except:
            pass  # Audit logging is optional
        
        conn.commit()
        conn.close()
        
        return True, f"Registration removed for {registration['firstname']} {registration['lastname']}"
        
    except Exception as e:
        return False, f"Error removing registration: {e}"

def bulk_remove_registrations(event_id, user_ids, admin_email, reason=""):
    """Remove multiple registrations at once"""
    try:
        removed_count = 0
        failed_count = 0
        
        for user_id in user_ids:
            success, message = remove_user_registration(event_id, user_id, admin_email, reason)
            if success:
                removed_count += 1
            else:
                failed_count += 1
        
        return True, f"Removed {removed_count} registrations. {failed_count} failed."
        
    except Exception as e:
        return False, f"Error in bulk removal: {e}"

def get_registration_audit_log(event_id=None, limit=50):
    """Get registration audit log"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        where_clause = ""
        params = []
        
        if event_id:
            where_clause = "WHERE event_id = %s"
            params.append(event_id)
        
        cur.execute(f"""
            SELECT * FROM registration_audit_log
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT %s
        """, params + [limit])
        
        logs = cur.fetchall()
        conn.close()
        
        return logs
        
    except Exception as e:
        print(f"Error getting audit log: {e}")
        return []

def show_registration_management(event_id, event_title, admin_email):
    """Show registration management interface for admins"""
    st.markdown(f"### 👥 Manage Registrations: {event_title}")
    
    # Get all participants
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        cur.execute("""
            SELECT 
                u.id as user_id,
                u.firstname,
                u.lastname,
                u.email,
                u.department,
                u.role,
                ep.registered_at,
                ep.status
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            WHERE ep.event_id = %s
            ORDER BY ep.registered_at ASC
        """, (event_id,))
        
        participants = cur.fetchall()
        conn.close()
        
    except Exception as e:
        st.error(f"Error loading participants: {e}")
        return
    
    if not participants:
        st.info("No participants registered for this event")
        return
    
    st.markdown(f"**Total Participants: {len(participants)}**")
    
    # Bulk actions
    st.markdown("#### 🔧 Bulk Actions")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 Export All"):
            df = pd.DataFrame(participants)
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download CSV",
                data=csv,
                file_name=f"event_{event_id}_participants.csv",
                mime="text/csv"
            )
    
    with col2:
        if st.button("📧 Email All"):
            st.session_state['show_bulk_email'] = True
    
    with col3:
        if st.button("🗑️ Bulk Remove"):
            st.session_state['show_bulk_remove'] = True
    
    # Bulk email form
    if st.session_state.get('show_bulk_email', False):
        with st.expander("📧 Send Bulk Email", expanded=True):
            from communication_system import show_participant_email_form
            show_participant_email_form(event_id, event_title, admin_email)
            
            if st.button("Close Email Form"):
                st.session_state['show_bulk_email'] = False
                st.rerun()
    
    # Bulk remove form
    if st.session_state.get('show_bulk_remove', False):
        with st.expander("🗑️ Bulk Remove Registrations", expanded=True):
            st.warning("⚠️ This will permanently remove selected registrations!")
            
            # Select participants to remove
            selected_participants = st.multiselect(
                "Select participants to remove:",
                options=[p['user_id'] for p in participants],
                format_func=lambda x: next(f"{p['firstname']} {p['lastname']} ({p['email']})" 
                                         for p in participants if p['user_id'] == x)
            )
            
            reason = st.text_area("Reason for removal (optional):", height=100)
            
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🗑️ Remove Selected", type="primary"):
                    if selected_participants:
                        success, message = bulk_remove_registrations(
                            event_id, selected_participants, admin_email, reason
                        )
                        if success:
                            st.success(message)
                            st.rerun()
                        else:
                            st.error(message)
                    else:
                        st.error("Please select participants to remove")
            
            with col2:
                if st.button("Cancel"):
                    st.session_state['show_bulk_remove'] = False
                    st.rerun()
    
    # Individual participant management
    st.markdown("#### 👤 Individual Participants")
    
    # Search and filter
    col1, col2 = st.columns(2)
    with col1:
        search_term = st.text_input("Search participants:", placeholder="Name or email")
    with col2:
        role_filter = st.selectbox("Filter by role:", ["All", "student", "alumni", "staff", "admin"])
    
    # Apply filters
    filtered_participants = participants
    
    if search_term:
        search_lower = search_term.lower()
        filtered_participants = [
            p for p in filtered_participants
            if (search_lower in p['firstname'].lower() or 
                search_lower in p['lastname'].lower() or 
                search_lower in p['email'].lower())
        ]
    
    if role_filter != "All":
        filtered_participants = [p for p in filtered_participants if p['role'] == role_filter]
    
    st.markdown(f"**Showing {len(filtered_participants)} of {len(participants)} participants**")
    
    # Display participants
    for i, participant in enumerate(filtered_participants):
        with st.container():
            col1, col2, col3 = st.columns([3, 2, 1])
            
            with col1:
                st.markdown(f"""
                **{participant['firstname']} {participant['lastname']}**
                
                📧 {participant['email']}
                
                🏢 {participant['department']} | {participant['role'].title()}
                
                📅 Registered: {participant['registered_at'].strftime('%B %d, %Y at %I:%M %p')}
                """)
            
            with col2:
                status_color = {"registered": "🟢", "cancelled": "🔴", "attended": "✅", "no_show": "⚪"}.get(participant['status'], "⚪")
                st.markdown(f"**Status:** {status_color} {participant['status'].title()}")
                
                # Status update
                new_status = st.selectbox(
                    "Update Status:",
                    ["registered", "cancelled", "attended", "no_show"],
                    index=["registered", "cancelled", "attended", "no_show"].index(participant['status']),
                    key=f"status_{participant['user_id']}"
                )
                
                if new_status != participant['status']:
                    if st.button("Update", key=f"update_status_{participant['user_id']}"):
                        try:
                            conn = get_connection()
                            cur = conn.cursor()
                            cur.execute("""
                                UPDATE event_participants 
                                SET status = %s 
                                WHERE event_id = %s AND user_id = %s
                            """, (new_status, event_id, participant['user_id']))
                            conn.commit()
                            conn.close()
                            st.success("Status updated!")
                            st.rerun()
                        except Exception as e:
                            st.error(f"Error updating status: {e}")
            
            with col3:
                # Individual actions
                if st.button("📧", key=f"email_{participant['user_id']}", help="Send Email"):
                    st.session_state[f"email_participant_{participant['user_id']}"] = True
                
                if st.button("👤", key=f"profile_{participant['user_id']}", help="View Profile"):
                    st.session_state[f"view_profile_{participant['user_id']}"] = True
                
                if st.button("🗑️", key=f"remove_{participant['user_id']}", help="Remove Registration"):
                    st.session_state[f"remove_participant_{participant['user_id']}"] = True
            
            # Individual email form
            if st.session_state.get(f"email_participant_{participant['user_id']}", False):
                with st.expander(f"📧 Email {participant['firstname']} {participant['lastname']}", expanded=True):
                    from communication_system import send_internal_message
                    
                    with st.form(f"email_form_{participant['user_id']}"):
                        subject = st.text_input("Subject", value=f"Regarding {event_title}")
                        message = st.text_area("Message", height=100)
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("Send Email"):
                                if subject and message:
                                    success, result = send_internal_message(
                                        admin_email, participant['email'], subject, message,
                                        "event_notification", event_id
                                    )
                                    if success:
                                        st.success("Email sent!")
                                        del st.session_state[f"email_participant_{participant['user_id']}"]
                                        st.rerun()
                                    else:
                                        st.error(result)
                                else:
                                    st.error("Subject and message required")
                        
                        with col2:
                            if st.form_submit_button("Cancel"):
                                del st.session_state[f"email_participant_{participant['user_id']}"]
                                st.rerun()
            
            # Individual profile view
            if st.session_state.get(f"view_profile_{participant['user_id']}", False):
                with st.expander(f"👤 Profile: {participant['firstname']} {participant['lastname']}", expanded=True):
                    from auth_system import show_user_profile
                    show_user_profile(participant['email'])
                    
                    if st.button("Close Profile", key=f"close_profile_{participant['user_id']}"):
                        del st.session_state[f"view_profile_{participant['user_id']}"]
                        st.rerun()
            
            # Individual removal confirmation
            if st.session_state.get(f"remove_participant_{participant['user_id']}", False):
                with st.expander(f"🗑️ Remove {participant['firstname']} {participant['lastname']}", expanded=True):
                    st.warning("⚠️ This will permanently remove this registration!")
                    
                    reason = st.text_area("Reason for removal:", height=80)
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("Confirm Removal", key=f"confirm_remove_{participant['user_id']}"):
                            success, message = remove_user_registration(
                                event_id, participant['user_id'], admin_email, reason
                            )
                            if success:
                                st.success(message)
                                del st.session_state[f"remove_participant_{participant['user_id']}"]
                                st.rerun()
                            else:
                                st.error(message)
                    
                    with col2:
                        if st.button("Cancel Removal", key=f"cancel_remove_{participant['user_id']}"):
                            del st.session_state[f"remove_participant_{participant['user_id']}"]
                            st.rerun()
            
            st.markdown("---")

def show_audit_log(event_id=None):
    """Show registration audit log"""
    st.markdown("### 📋 Registration Audit Log")
    
    logs = get_registration_audit_log(event_id)
    
    if not logs:
        st.info("No audit log entries found")
        return
    
    # Display logs
    for log in logs:
        action_color = {"REMOVED": "🔴", "ADDED": "🟢", "MODIFIED": "🟡"}.get(log['action'], "⚪")
        
        with st.expander(f"{action_color} {log['action']} - {log['user_email']} - {log['timestamp'].strftime('%B %d, %Y at %I:%M %p')}"):
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown(f"""
                **User:** {log['user_email']}
                **Event:** {log['event_title']}
                **Action:** {log['action']}
                """)
            
            with col2:
                st.markdown(f"""
                **Admin:** {log['admin_email']}
                **Timestamp:** {log['timestamp'].strftime('%B %d, %Y at %I:%M %p')}
                """)
            
            if log['reason']:
                st.markdown(f"**Reason:** {log['reason']}")

def get_system_statistics():
    """Get comprehensive system statistics"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        stats = {}
        
        # User statistics
        cur.execute("""
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN is_active THEN 1 END) as active_users,
                COUNT(CASE WHEN is_confirmed THEN 1 END) as confirmed_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
                COUNT(CASE WHEN role = 'staff' THEN 1 END) as staff_users,
                COUNT(CASE WHEN role = 'student' THEN 1 END) as student_users
            FROM users
        """)
        stats['users'] = cur.fetchone()
        
        # Event statistics
        cur.execute("""
            SELECT 
                COUNT(*) as total_events,
                COUNT(CASE WHEN event_date >= CURRENT_DATE THEN 1 END) as upcoming_events,
                COUNT(CASE WHEN registration_deadline >= CURRENT_DATE THEN 1 END) as open_registration
            FROM events
        """)
        stats['events'] = cur.fetchone()
        
        # Registration statistics
        cur.execute("""
            SELECT 
                COUNT(*) as total_registrations,
                COUNT(DISTINCT user_id) as unique_participants,
                COUNT(DISTINCT event_id) as events_with_registrations
            FROM event_participants
        """)
        stats['registrations'] = cur.fetchone()
        
        # Message statistics
        cur.execute("""
            SELECT 
                COUNT(*) as total_messages,
                COUNT(CASE WHEN is_read THEN 1 END) as read_messages,
                COUNT(CASE WHEN message_type = 'event_notification' THEN 1 END) as event_notifications
            FROM internal_messages
        """)
        stats['messages'] = cur.fetchone()
        
        conn.close()
        return stats
        
    except Exception as e:
        print(f"Error getting system statistics: {e}")
        return None

def show_system_dashboard():
    """Show comprehensive system dashboard for admins"""
    st.markdown("### 📊 System Dashboard")
    
    stats = get_system_statistics()
    
    if not stats:
        st.error("Unable to load system statistics")
        return
    
    # User metrics
    st.markdown("#### 👥 User Statistics")
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Users", stats['users']['total_users'])
    with col2:
        st.metric("Active Users", stats['users']['active_users'])
    with col3:
        st.metric("Confirmed Users", stats['users']['confirmed_users'])
    with col4:
        st.metric("Admin Users", stats['users']['admin_users'])
    
    # Event metrics
    st.markdown("#### 🎉 Event Statistics")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Events", stats['events']['total_events'])
    with col2:
        st.metric("Upcoming Events", stats['events']['upcoming_events'])
    with col3:
        st.metric("Open Registration", stats['events']['open_registration'])
    
    # Registration metrics
    st.markdown("#### 🎫 Registration Statistics")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Registrations", stats['registrations']['total_registrations'])
    with col2:
        st.metric("Unique Participants", stats['registrations']['unique_participants'])
    with col3:
        st.metric("Events with Registrations", stats['registrations']['events_with_registrations'])
    
    # Message metrics
    st.markdown("#### 📧 Communication Statistics")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Total Messages", stats['messages']['total_messages'])
    with col2:
        st.metric("Read Messages", stats['messages']['read_messages'])
    with col3:
        st.metric("Event Notifications", stats['messages']['event_notifications'])
