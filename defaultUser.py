from db import register_user  # Import the function to register users in the database

def add_default_users():
    roles = ["admin", "staff", "student", "alumni"]
    default_department = "General"
    default_interests = None
    default_graduation_year = 2023  # Use a placeholder year for alumni only

    for role in roles:
        email = f"{role}@example.com"
        password = f"{role}@123"
        graduation_year = default_graduation_year if role == "alumni" else None  # Explicitly set None for non-alumni
        try:
            # Include additional arguments: department, interests, and graduation_year
            register_user(email, password, role, default_department, default_interests, graduation_year)
            print(f"Default user added: {email} with role {role}")
        except Exception as e:
            print(f"Failed to add user {email}: {e}")

if __name__ == "__main__":
    add_default_users()
