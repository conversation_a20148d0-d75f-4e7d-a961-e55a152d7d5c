#!/usr/bin/env python3
"""
Script to test AI recommendations functionality
"""

from db import get_connection
import psycopg2.extras

def test_ai_recommendations():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get user IDs for testing
        cur.execute("SELECT id, email FROM users")
        users = cur.fetchall()
        
        print("Testing AI recommendations generation...")
        
        # Add sample recommendations
        sample_recommendations = [
            "Based on your Computer Science background, consider joining the Tech Alumni Network for career opportunities.",
            "Your interest in networking events suggests you might enjoy our monthly alumni meetups.",
            "Given your recent activity, you might be interested in mentoring current students in your field.",
            "Consider updating your profile to include your current job title for better networking opportunities."
        ]
        
        for i, user in enumerate(users):
            recommendation_text = sample_recommendations[i % len(sample_recommendations)]
            
            cur.execute("""
                INSERT INTO recommendations (user_id, recommended_text, generated_at)
                VALUES (%s, %s, NOW())
            """, (user['id'], recommendation_text))
            
            print(f"  ✅ Generated recommendation for {user['email']}")
        
        conn.commit()
        
        # Test retrieving recommendations
        cur.execute("""
            SELECT r.recommended_text, u.email, r.generated_at
            FROM recommendations r
            JOIN users u ON r.user_id = u.id
            ORDER BY r.generated_at DESC
        """)
        
        recommendations = cur.fetchall()
        print(f"\nRetrieved {len(recommendations)} recommendations:")
        
        for rec in recommendations:
            print(f"  {rec['email']}: \"{rec['recommended_text'][:60]}...\"")
        
        # Test recommendation analytics
        cur.execute("""
            SELECT COUNT(*) as total_recommendations,
                   COUNT(DISTINCT user_id) as users_with_recommendations
            FROM recommendations
        """)
        
        stats = cur.fetchone()
        print(f"\nRecommendation Statistics:")
        print(f"  Total recommendations: {stats['total_recommendations']}")
        print(f"  Users with recommendations: {stats['users_with_recommendations']}")
        
        conn.close()
        print("\nAI recommendations test completed successfully!")
        
    except Exception as e:
        print(f"Error testing AI recommendations: {e}")

if __name__ == "__main__":
    test_ai_recommendations()
