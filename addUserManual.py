import psycopg2
import bcrypt

try:
    # Connection
    conn = psycopg2.connect(
        dbname="alumni",
        user="postgres",
        password="alfred",
        host="localhost",
        port="5432"
    )
    cur = conn.cursor()

    # User data
    firstname = "<PERSON><PERSON>"
    lastname = "<PERSON>"
    email="<EMAIL>"
    password = "staff123"
    role = "staff"
    department = "Computer Science"
    interests = ["AI", "ML"]
    current_position = "Software Engineer"
    graduation_year = 2023

    # Hash the password
    password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

    # Insert into users table
    sql = "INSERT INTO users (firstname, lastname, email, password_hash, role, department, interests, current_position, graduation_year) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)"
    data = (firstname, lastname, email, password_hash, role, department, interests, current_position, graduation_year)

    cur.execute(sql, data)
    conn.commit()
    print("User added successfully.")

except psycopg2.Error as e:
    print(f"Database error: {e}")
finally:
    if cur:
        cur.close()
    if conn:
        conn.close()