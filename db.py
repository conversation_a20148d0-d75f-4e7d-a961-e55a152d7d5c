import psycopg2
import pandas as pd
import bcrypt

# 🔌 PostgreSQL DB connection
def get_connection():
    """
    Establish a connection to the PostgreSQL database.
    Ensure the credentials are correct.
    """
    return psycopg2.connect(
        host="localhost",  # Ensure this is the correct host
        database="alumni",  # Ensure this is the correct database name
        user="postgres",  # Ensure this is the correct username
        password="alfred",  # Ensure this is the correct password
        port="5432"  # Ensure this is the correct port
    )

# 🔐 Authentication
def authenticate_user(email, password):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT password_hash FROM users WHERE email = %s", (email,))
            result = cur.fetchone()
            if result and bcrypt.checkpw(password.encode(), result[0].encode()):
                return True
    return False

def get_user_permissions(email):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT role FROM users WHERE email = %s", (email,))
            result = cur.fetchone()
            if result:
                role = result[0]
                # Assign permissions based on role
                if role == "admin":
                    return ["manage_users", "view_reports", "chat", "recommend"]
                elif role == "staff":
                    return ["view_reports", "chat"]
                elif role == "student":
                    return ["chat", "feedback", "recommend"]
                elif role == "alumni":
                    return ["chat", "feedback"]
    return []



# 👤 USER OPERATIONS
def register_user(firstname, lastname, email, password, role, department, interests, current_position, graduation_year):
    hashed_pw = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()  # Hash the password
    with get_connection() as conn:
        with conn.cursor() as cur:
            try:
                cur.execute('''
                    INSERT INTO users (firstname, lastname, email, password_hash, role, department, interests, current_position, graduation_year)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', (firstname, lastname, email, hashed_pw, role, department, interests, current_position, graduation_year))
                conn.commit()
                print(f"User {email} added successfully.")
            except psycopg2.Error as e:
                print(f"Failed to add user {email}: {e}")
                raise

def update_user_role(email, new_role):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute('''
                UPDATE users
                SET role = %s
                WHERE email = %s
            ''', (new_role, email))
            conn.commit()

def delete_user_by_email(email):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM users WHERE email = %s", (email,))
            conn.commit()

def get_all_users():
    with get_connection() as conn:
        return pd.read_sql("SELECT * FROM users", conn)

def delete_user(user_id):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM users WHERE id = %s", (user_id,))
            conn.commit()

def get_user_by_email(email):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute('''
                SELECT firstname, lastname, email, role, department, interests, current_position, graduation_year
                FROM users
                WHERE email = %s
            ''', (email,))
            result = cur.fetchone()
            if result:
                return {
                    "firstname": result[0],
                    "lastname": result[1],
                    "email": result[2],
                    "role": result[3],
                    "department": result[4],
                    "interests": result[5],
                    "current_position": result[6],
                    "graduation_year": result[7]
                }
            return None

def update_user_details(email, **kwargs):
    """
    Update a user's details in the PostgreSQL database.
    """
    with get_connection() as conn:
        with conn.cursor() as cur:
            updates = ", ".join([f"{key} = %s" for key in kwargs.keys()])
            values = list(kwargs.values()) + [email]
            query = f"UPDATE users SET {updates} WHERE email = %s"
            cur.execute(query, values)
        conn.commit()

# 📚 COURSES
def insert_course(course_code, course_name, department):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute('''
                INSERT INTO courses (course_code, course_name, department)
                VALUES (%s, %s, %s)
            ''', (course_code, course_name, department))
            conn.commit()

def get_courses():
    with get_connection() as conn:
        return pd.read_sql("SELECT * FROM courses", conn)

# 📘 USER COURSES
def add_user_course(user_id, course_id):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("INSERT INTO user_courses (user_id, course_id) VALUES (%s, %s)", (user_id, course_id))
            conn.commit()

def get_user_courses(user_id):
    with get_connection() as conn:
        return pd.read_sql("SELECT * FROM user_courses WHERE user_id = %s", conn, params=(user_id,))

# 💬 CHAT SYSTEM
def send_message(sender_id, receiver_id, message):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute('''
                INSERT INTO chats (sender_id, receiver_id, message) VALUES (%s, %s, %s)
            ''', (sender_id, receiver_id, message))
            conn.commit()

def get_messages(user_id):
    with get_connection() as conn:
        return pd.read_sql('''
            SELECT * FROM chats
            WHERE sender_id = %s OR receiver_id = %s
            ORDER BY sent_at DESC
        ''', conn, params=(user_id, user_id))

# 📝 FEEDBACK
def submit_feedback(user_id, text):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("INSERT INTO feedback (user_id, feedback_text) VALUES (%s, %s)", (user_id, text))
            conn.commit()

def get_all_feedback():
    with get_connection() as conn:
        return pd.read_sql("SELECT * FROM feedback", conn)

# 🤖 RECOMMENDATIONS
def insert_recommendation(user_id, recommended_text):
    with get_connection() as conn:
        with conn.cursor() as cur:
            cur.execute("INSERT INTO recommendations (user_id, recommended_text) VALUES (%s, %s)", (user_id, recommended_text))
            conn.commit()

def get_user_recommendations(user_id):
    with get_connection() as conn:
        return pd.read_sql("SELECT * FROM recommendations WHERE user_id = %s", conn, params=(user_id,))
