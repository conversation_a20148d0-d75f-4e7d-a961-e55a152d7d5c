#!/usr/bin/env python3
"""
Test script for enhanced presentation features
Tests the improved UI/UX for Events and User Management
"""

import subprocess
import sys
from datetime import datetime

def check_enhanced_presentation_syntax():
    """Check syntax of enhanced presentation files"""
    print("🔍 Checking Enhanced Presentation Syntax...")
    
    files_to_check = [
        'enhanced_presentation.py',
        'app.py',
        'event_management.py'
    ]
    
    for file in files_to_check:
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', file], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"  ✅ {file} - Syntax OK")
            else:
                print(f"  ❌ {file} - Syntax Error: {result.stderr}")
                return False
        except FileNotFoundError:
            print(f"  ⚠️ {file} - File not found")
    
    return True

def test_enhanced_events_integration():
    """Test enhanced events integration"""
    print("\n🎉 Testing Enhanced Events Integration...")
    
    try:
        # Test import of enhanced presentation
        from enhanced_presentation import show_enhanced_all_events_tab, apply_enhanced_filters
        print("  ✅ Enhanced events functions imported successfully")
        
        # Test filter functions
        sample_events = [
            {'title': 'Test Event', 'status': 'open', 'event_date': datetime.now().date()},
            {'title': 'Another Event', 'status': 'closed', 'event_date': datetime.now().date()}
        ]
        
        filtered = apply_enhanced_filters(sample_events, "All", "All", "All", "")
        print(f"  ✅ Filter function working - {len(filtered)} events processed")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Enhanced events import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Enhanced events test error: {e}")
        return False

def test_enhanced_user_management_integration():
    """Test enhanced user management integration"""
    print("\n👥 Testing Enhanced User Management Integration...")
    
    try:
        # Test import of enhanced user management
        from enhanced_presentation import (
            show_enhanced_user_management, 
            fetch_filtered_users,
            show_user_statistics_dashboard
        )
        print("  ✅ Enhanced user management functions imported successfully")
        
        # Test helper functions exist
        helper_functions = [
            'toggle_user_status',
            'delete_user', 
            'update_user_details',
            'create_new_user',
            'export_users_to_csv'
        ]
        
        for func_name in helper_functions:
            try:
                from enhanced_presentation import __dict__ as ep_dict
                if func_name in ep_dict:
                    print(f"  ✅ {func_name} function available")
                else:
                    print(f"  ⚠️ {func_name} function not found")
            except:
                print(f"  ⚠️ Could not check {func_name}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Enhanced user management import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Enhanced user management test error: {e}")
        return False

def test_ui_components_integration():
    """Test UI components integration"""
    print("\n🎨 Testing UI Components Integration...")
    
    try:
        from ui_components import create_professional_card, create_metric_card, create_status_badge
        print("  ✅ UI components imported successfully")
        
        # Test component creation
        card_html = create_professional_card("Test Title", "Test content")
        metric_html = create_metric_card("100", "Test Metric", "📊", "primary")
        badge_html = create_status_badge("active", "Active Status")
        
        print("  ✅ UI component creation working")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ UI components import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ UI components test error: {e}")
        return False

def test_database_compatibility():
    """Test database compatibility with enhanced features"""
    print("\n🗄️ Testing Database Compatibility...")
    
    try:
        from db import get_connection
        conn = get_connection()
        cur = conn.cursor()
        
        # Test enhanced user fields
        cur.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'users' 
            AND column_name IN ('is_active', 'is_confirmed', 'confirmed_by', 'confirmed_at')
        """)
        enhanced_fields = cur.fetchall()
        print(f"  ✅ Enhanced user fields available: {len(enhanced_fields)}")
        
        # Test events table structure
        cur.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'events'
            AND column_name IN ('registration_deadline', 'max_participants', 'created_at')
        """)
        event_fields = cur.fetchall()
        print(f"  ✅ Enhanced event fields available: {len(event_fields)}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"  ❌ Database compatibility error: {e}")
        return False

def test_presentation_features():
    """Test specific presentation features"""
    print("\n✨ Testing Presentation Features...")
    
    features_tested = 0
    features_passed = 0
    
    # Test 1: Enhanced event cards
    features_tested += 1
    try:
        from enhanced_presentation import show_enhanced_event_card
        print("  ✅ Enhanced event cards available")
        features_passed += 1
    except:
        print("  ⚠️ Enhanced event cards not available")
    
    # Test 2: User browser with pagination
    features_tested += 1
    try:
        from enhanced_presentation import display_users_with_pagination
        print("  ✅ User pagination system available")
        features_passed += 1
    except:
        print("  ⚠️ User pagination system not available")
    
    # Test 3: Advanced filtering
    features_tested += 1
    try:
        from enhanced_presentation import apply_enhanced_filters, sort_events
        print("  ✅ Advanced filtering system available")
        features_passed += 1
    except:
        print("  ⚠️ Advanced filtering system not available")
    
    # Test 4: User analytics
    features_tested += 1
    try:
        from enhanced_presentation import show_detailed_user_analytics
        print("  ✅ User analytics dashboard available")
        features_passed += 1
    except:
        print("  ⚠️ User analytics dashboard not available")
    
    # Test 5: Enhanced user creation
    features_tested += 1
    try:
        from enhanced_presentation import show_enhanced_user_creator
        print("  ✅ Enhanced user creation interface available")
        features_passed += 1
    except:
        print("  ⚠️ Enhanced user creation interface not available")
    
    success_rate = (features_passed / features_tested) * 100
    print(f"  📊 Presentation features: {features_passed}/{features_tested} available ({success_rate:.1f}%)")
    
    return success_rate >= 80

def test_integration_with_main_app():
    """Test integration with main application"""
    print("\n🔗 Testing Integration with Main App...")
    
    try:
        # Check if main app can import enhanced features
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        integration_checks = [
            'from enhanced_presentation import show_enhanced_user_management',
            'show_enhanced_user_management(role)',
            'from enhanced_presentation import show_enhanced_all_events_tab'
        ]
        
        found_integrations = 0
        for check in integration_checks:
            if check in app_content:
                found_integrations += 1
                print(f"  ✅ Found: {check}")
            else:
                print(f"  ⚠️ Missing: {check}")
        
        # Check event management integration
        with open('event_management.py', 'r', encoding='utf-8') as f:
            event_content = f.read()
        
        if 'show_enhanced_all_events_tab' in event_content:
            found_integrations += 1
            print("  ✅ Event management integration found")
        else:
            print("  ⚠️ Event management integration missing")
        
        integration_rate = (found_integrations / (len(integration_checks) + 1)) * 100
        print(f"  📊 Integration: {found_integrations}/{len(integration_checks) + 1} checks passed ({integration_rate:.1f}%)")
        
        return integration_rate >= 75
        
    except Exception as e:
        print(f"  ❌ Integration test error: {e}")
        return False

def run_comprehensive_presentation_test():
    """Run comprehensive test of enhanced presentation features"""
    print("🚀 COMPREHENSIVE ENHANCED PRESENTATION TEST")
    print("=" * 70)
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Syntax Check", check_enhanced_presentation_syntax),
        ("Enhanced Events Integration", test_enhanced_events_integration),
        ("Enhanced User Management", test_enhanced_user_management_integration),
        ("UI Components Integration", test_ui_components_integration),
        ("Database Compatibility", test_database_compatibility),
        ("Presentation Features", test_presentation_features),
        ("Main App Integration", test_integration_with_main_app)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_function():
                passed_tests += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 TEST SUMMARY: {passed_tests}/{total_tests} tests passed ({(passed_tests/total_tests)*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED! Enhanced presentation features are fully functional!")
        print("\n🌟 ENHANCED FEATURES VERIFIED:")
        print("  ✅ Professional event cards with advanced filtering")
        print("  ✅ Enhanced user management with search and pagination")
        print("  ✅ Professional UI components and styling")
        print("  ✅ Database compatibility with enhanced fields")
        print("  ✅ Comprehensive user analytics and reporting")
        print("  ✅ Seamless integration with main application")
        
    elif passed_tests >= total_tests * 0.8:
        print("✅ Most tests passed! Enhanced presentation is largely functional.")
        print("\n🎯 IMPROVEMENTS IMPLEMENTED:")
        print("  ✅ Better visual presentation for events and users")
        print("  ✅ Advanced search and filtering capabilities")
        print("  ✅ Professional UI components and styling")
        print("  ✅ Enhanced user experience and navigation")
        
    else:
        print("⚠️ Some tests failed. Please review the issues above.")
    
    print(f"\nTest completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = run_comprehensive_presentation_test()
    sys.exit(0 if success else 1)
