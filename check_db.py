#!/usr/bin/env python3
"""
Script to check database structure and existing data
"""

from db import get_connection
import psycopg2.extras

def check_database():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Get all table names
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = cur.fetchall()
        print('Existing tables:')
        for table in tables:
            print(f'- {table["table_name"]}')

        # Check structure of key tables
        key_tables = ['users', 'chats', 'feedback', 'events', 'event_participants', 'recommendations']
        for table_name in key_tables:
            try:
                cur.execute(f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                """)
                columns = cur.fetchall()
                if columns:
                    print(f'\n{table_name.upper()} table structure:')
                    for col in columns:
                        default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
                        print(f'  {col["column_name"]} ({col["data_type"]}) - Nullable: {col["is_nullable"]}{default}')
                else:
                    print(f'\n{table_name.upper()} table does not exist')
            except Exception as e:
                print(f'Error checking {table_name}: {e}')

        # Check sample data from users table
        try:
            cur.execute("SELECT COUNT(*) as count FROM users")
            user_count = cur.fetchone()
            print(f'\nUsers table has {user_count["count"]} records')
            
            if user_count["count"] > 0:
                cur.execute("SELECT email, role, firstname, lastname FROM users LIMIT 5")
                sample_users = cur.fetchall()
                print("Sample users:")
                for user in sample_users:
                    print(f'  {user["email"]} ({user["role"]}) - {user["firstname"]} {user["lastname"]}')
        except Exception as e:
            print(f'Error checking users data: {e}')

        conn.close()
        print("\nDatabase check completed successfully!")
        
    except Exception as e:
        print(f"Database connection error: {e}")

if __name__ == "__main__":
    check_database()
