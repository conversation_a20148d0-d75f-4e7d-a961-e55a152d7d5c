#!/usr/bin/env python3
"""
Test script for enhanced AI recommendation system
"""

from enhanced_ai_engine import EnhancedRecommendationEngine, train_enhanced_models, get_enhanced_recommendations
from db import get_connection
import psycopg2.extras

def test_enhanced_ai_system():
    print("🚀 Testing Enhanced AI Recommendation System")
    print("=" * 60)
    
    # Initialize the engine
    engine = EnhancedRecommendationEngine()
    
    # Test 1: Load training data
    print("\n1. Testing Training Data Loading...")
    success = engine.load_training_data()
    if success:
        print(f"   ✅ Training data loaded: {len(engine.training_data)} records")
        print(f"   📊 Categories: {engine.training_data['recommendation_type'].unique()}")
    else:
        print("   ❌ Failed to load training data")
        return
    
    # Test 2: Train models
    print("\n2. Testing Model Training...")
    try:
        success = engine.train_content_based_model()
        if success:
            print("   ✅ Content-based model trained successfully")
        else:
            print("   ❌ Failed to train content-based model")
    except Exception as e:
        print(f"   ❌ Error training model: {e}")
    
    # Test 3: Test recommendations for different users
    print("\n3. Testing Recommendations for Different Users...")
    
    test_users = [
        ("<EMAIL>", "Admin with AI/ML interests"),
        ("<EMAIL>", "Student with AI/ML interests"),
        ("<EMAIL>", "Staff with AI/ML/GIS interests"),
        ("<EMAIL>", "Student with swimming/jogging interests")
    ]
    
    for email, description in test_users:
        print(f"\n   Testing {description} ({email}):")
        try:
            recommendations = get_enhanced_recommendations(email)
            
            print(f"   📝 Personalized message: {recommendations.get('personalized_message', 'None')[:100]}...")
            
            content_recs = recommendations.get('content_based', [])
            print(f"   🎯 Content-based recommendations: {len(content_recs)}")
            for i, rec in enumerate(content_recs[:2], 1):
                print(f"      {i}. {rec['type']}: {rec['text'][:60]}...")
            
            networking_recs = recommendations.get('networking', [])
            print(f"   👥 Networking recommendations: {len(networking_recs)}")
            for i, rec in enumerate(networking_recs[:2], 1):
                print(f"      {i}. {rec['text'][:60]}...")
            
            reading_recs = recommendations.get('reading', [])
            print(f"   📚 Reading recommendations: {len(reading_recs)}")
            for i, rec in enumerate(reading_recs[:2], 1):
                print(f"      {i}. {rec['text'][:60]}...")
                
        except Exception as e:
            print(f"   ❌ Error getting recommendations: {e}")
    
    # Test 4: Test interest preprocessing
    print("\n4. Testing Interest Preprocessing...")
    test_interests = [
        ["AI", "Machine Learning", "Data Science"],
        "Software Engineering, Programming",
        ["Business", "Entrepreneurship"],
        "swimming, jogging"
    ]
    
    for interests in test_interests:
        processed = engine.preprocess_interests(interests)
        print(f"   Input: {interests}")
        print(f"   Processed: {processed}")
    
    # Test 5: Test collaborative filtering
    print("\n5. Testing Collaborative Filtering...")
    try:
        collab_recs = engine.get_collaborative_recommendations("<EMAIL>")
        print(f"   📊 Collaborative recommendations: {len(collab_recs)}")
        for rec in collab_recs:
            print(f"      • {rec['text'][:60]}...")
    except Exception as e:
        print(f"   ❌ Error in collaborative filtering: {e}")
    
    # Test 6: Test networking recommendations
    print("\n6. Testing Networking Recommendations...")
    try:
        network_recs = engine.get_networking_recommendations("<EMAIL>")
        print(f"   🤝 Networking recommendations: {len(network_recs)}")
        for rec in network_recs:
            print(f"      • {rec['text'][:60]}...")
    except Exception as e:
        print(f"   ❌ Error in networking recommendations: {e}")
    
    print("\n" + "=" * 60)
    print("✅ Enhanced AI Recommendation System Test Completed!")

def test_model_performance():
    print("\n🧪 Testing Model Performance...")
    
    try:
        engine = EnhancedRecommendationEngine()
        if not engine.load_training_data():
            print("❌ Cannot test performance without training data")
            return
        
        # Test with different interest combinations
        test_cases = [
            "AI, Machine Learning, Data Science",
            "Software Engineering, Programming, Web Development",
            "Business, Entrepreneurship, Marketing",
            "Design, UX/UI, Creative Arts",
            "Healthcare, Medicine, Biotechnology"
        ]
        
        print("Testing recommendation accuracy for different interest profiles:")
        
        for interests in test_cases:
            print(f"\n   Interest Profile: {interests}")
            
            # Get content-based recommendations
            recs = engine.get_content_based_recommendations(interests, num_recommendations=3)
            
            print(f"   Generated {len(recs)} recommendations:")
            for i, rec in enumerate(recs, 1):
                print(f"      {i}. [{rec['type']}] {rec['text'][:50]}... (Priority: {rec['priority']})")
        
        print("\n✅ Model performance test completed!")
        
    except Exception as e:
        print(f"❌ Error testing model performance: {e}")

def main():
    test_enhanced_ai_system()
    test_model_performance()

if __name__ == "__main__":
    main()
