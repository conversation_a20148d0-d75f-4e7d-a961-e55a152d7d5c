#!/usr/bin/env python3
"""
Script to test feedback functionality
"""

from db import get_connection
import psycopg2.extras

def test_feedback_functionality():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Get user IDs for testing
        cur.execute("SELECT id, email FROM users LIMIT 3")
        users = cur.fetchall()
        
        print("Testing feedback submission...")
        
        # Add sample feedback
        sample_feedback = [
            ("General", "The alumni app is very useful for staying connected!", 5),
            ("Feature Request", "Would love to see more networking features", 4),
            ("Bug Report", "Sometimes the chat doesn't load properly", 3)
        ]
        
        for i, (feedback_type, feedback_text, rating) in enumerate(sample_feedback):
            user = users[i % len(users)]
            
            cur.execute("""
                INSERT INTO feedback (user_id, feedback_type, feedback_text, rating, submitted_at)
                VALUES (%s, %s, %s, %s, NOW())
            """, (user['id'], feedback_type, feedback_text, rating))
            
            print(f"  ✅ Added {feedback_type} feedback from {user['email']}")
        
        conn.commit()
        
        # Test retrieving feedback
        cur.execute("""
            SELECT f.feedback_type, f.feedback_text, f.rating, u.email, f.submitted_at
            FROM feedback f
            JOIN users u ON f.user_id = u.id
            ORDER BY f.submitted_at DESC
        """)
        
        feedback_list = cur.fetchall()
        print(f"\nRetrieved {len(feedback_list)} feedback entries:")
        
        for feedback in feedback_list:
            print(f"  {feedback['email']}: {feedback['feedback_type']} (Rating: {feedback['rating']})")
            print(f"    \"{feedback['feedback_text'][:60]}...\"")
        
        conn.close()
        print("\nFeedback system test completed successfully!")
        
    except Exception as e:
        print(f"Error testing feedback: {e}")

if __name__ == "__main__":
    test_feedback_functionality()
