#!/usr/bin/env python3
"""
Enhanced Event Management System for Alumni App
Provides comprehensive event functionality with professional UI
"""

import streamlit as st
import pandas as pd
from datetime import datetime, date, timedelta
from db import get_connection
import psycopg2.extras
from ui_components import (
    create_page_header, create_metric_card, create_status_badge, 
    create_professional_card, show_alert, create_action_buttons
)

def get_user_id_by_email(email):
    """Get user ID by email"""
    try:
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("SELECT id FROM users WHERE email = %s", (email,))
            result = cur.fetchone()
        conn.close()
        return result['id'] if result else None
    except Exception as e:
        st.error(f"Error getting user ID: {e}")
        return None

def get_all_events():
    """Get all events with registration counts"""
    try:
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    e.*,
                    COUNT(ep.user_id) as registration_count,
                    CASE 
                        WHEN e.registration_deadline < CURRENT_DATE THEN 'closed'
                        WHEN e.event_date < CURRENT_DATE THEN 'completed'
                        ELSE 'open'
                    END as status
                FROM events e
                LEFT JOIN event_participants ep ON e.id = ep.event_id
                GROUP BY e.id, e.title, e.description, e.event_date, e.location, 
                         e.registration_deadline, e.max_participants, e.created_at
                ORDER BY e.event_date ASC
            """)
            events = cur.fetchall()
        conn.close()
        return events
    except Exception as e:
        st.error(f"Error fetching events: {e}")
        return []

def get_user_registrations(user_email):
    """Get user's event registrations"""
    try:
        user_id = get_user_id_by_email(user_email)
        if not user_id:
            return []
        
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    e.*,
                    ep.registered_at,
                    ep.status as registration_status,
                    CASE 
                        WHEN e.registration_deadline < CURRENT_DATE THEN 'closed'
                        WHEN e.event_date < CURRENT_DATE THEN 'completed'
                        ELSE 'open'
                    END as event_status
                FROM events e
                JOIN event_participants ep ON e.id = ep.event_id
                WHERE ep.user_id = %s
                ORDER BY e.event_date ASC
            """, (user_id,))
            registrations = cur.fetchall()
        conn.close()
        return registrations
    except Exception as e:
        st.error(f"Error fetching user registrations: {e}")
        return []

def register_for_event(user_email, event_id):
    """Register user for an event"""
    try:
        user_id = get_user_id_by_email(user_email)
        if not user_id:
            return False, "User not found"
        
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            # Check if already registered
            cur.execute("""
                SELECT id FROM event_participants 
                WHERE user_id = %s AND event_id = %s
            """, (user_id, event_id))
            
            if cur.fetchone():
                conn.close()
                return False, "Already registered for this event"
            
            # Check event capacity and deadline
            cur.execute("""
                SELECT 
                    e.max_participants,
                    e.registration_deadline,
                    COUNT(ep.user_id) as current_registrations
                FROM events e
                LEFT JOIN event_participants ep ON e.id = ep.event_id
                WHERE e.id = %s
                GROUP BY e.id, e.max_participants, e.registration_deadline
            """, (event_id,))
            
            event_info = cur.fetchone()
            if not event_info:
                conn.close()
                return False, "Event not found"
            
            # Check deadline
            if event_info['registration_deadline'] and event_info['registration_deadline'] < date.today():
                conn.close()
                return False, "Registration deadline has passed"
            
            # Check capacity
            if (event_info['max_participants'] and 
                event_info['current_registrations'] >= event_info['max_participants']):
                conn.close()
                return False, "Event is full"
            
            # Register user
            cur.execute("""
                INSERT INTO event_participants (event_id, user_id, registered_at, status)
                VALUES (%s, %s, CURRENT_TIMESTAMP, 'registered')
            """, (event_id, user_id))
            
        conn.commit()
        conn.close()
        return True, "Successfully registered for event"
        
    except Exception as e:
        return False, f"Error registering for event: {e}"

def cancel_registration(user_email, event_id):
    """Cancel user's event registration"""
    try:
        user_id = get_user_id_by_email(user_email)
        if not user_id:
            return False, "User not found"
        
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            # Check if registration exists and deadline
            cur.execute("""
                SELECT e.registration_deadline, ep.id
                FROM events e
                JOIN event_participants ep ON e.id = ep.event_id
                WHERE ep.user_id = %s AND ep.event_id = %s
            """, (user_id, event_id))
            
            result = cur.fetchone()
            if not result:
                conn.close()
                return False, "Registration not found"
            
            # Check deadline
            if result['registration_deadline'] and result['registration_deadline'] < date.today():
                conn.close()
                return False, "Cannot cancel after registration deadline"
            
            # Cancel registration
            cur.execute("""
                DELETE FROM event_participants 
                WHERE user_id = %s AND event_id = %s
            """, (user_id, event_id))
            
        conn.commit()
        conn.close()
        return True, "Registration cancelled successfully"
        
    except Exception as e:
        return False, f"Error cancelling registration: {e}"

def create_event(title, description, event_date, location, registration_deadline=None, max_participants=None):
    """Create a new event"""
    try:
        conn = get_connection()
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO events (title, description, event_date, location, 
                                  registration_deadline, max_participants, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            """, (title, description, event_date, location, registration_deadline, max_participants))
        
        conn.commit()
        conn.close()
        return True, "Event created successfully"
        
    except Exception as e:
        return False, f"Error creating event: {e}"

def update_event(event_id, title, description, event_date, location, registration_deadline=None, max_participants=None):
    """Update an existing event"""
    try:
        conn = get_connection()
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE events 
                SET title = %s, description = %s, event_date = %s, location = %s,
                    registration_deadline = %s, max_participants = %s
                WHERE id = %s
            """, (title, description, event_date, location, registration_deadline, max_participants, event_id))
        
        conn.commit()
        conn.close()
        return True, "Event updated successfully"
        
    except Exception as e:
        return False, f"Error updating event: {e}"

def delete_event(event_id):
    """Delete an event and all its registrations"""
    try:
        conn = get_connection()
        with conn.cursor() as cur:
            # Delete registrations first
            cur.execute("DELETE FROM event_participants WHERE event_id = %s", (event_id,))
            # Delete event
            cur.execute("DELETE FROM events WHERE id = %s", (event_id,))
        
        conn.commit()
        conn.close()
        return True, "Event deleted successfully"
        
    except Exception as e:
        return False, f"Error deleting event: {e}"

def get_event_participants(event_id):
    """Get all participants for an event"""
    try:
        conn = get_connection()
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT 
                    u.firstname,
                    u.lastname,
                    u.email,
                    u.department,
                    u.role,
                    ep.registered_at,
                    ep.status
                FROM event_participants ep
                JOIN users u ON ep.user_id = u.id
                WHERE ep.event_id = %s
                ORDER BY ep.registered_at ASC
            """, (event_id,))
            participants = cur.fetchall()
        conn.close()
        return participants
    except Exception as e:
        st.error(f"Error fetching event participants: {e}")
        return []

def format_event_date(event_date):
    """Format event date for display"""
    if isinstance(event_date, str):
        event_date = datetime.strptime(event_date, '%Y-%m-%d').date()
    
    today = date.today()
    days_diff = (event_date - today).days
    
    if days_diff == 0:
        return f"Today ({event_date.strftime('%B %d, %Y')})"
    elif days_diff == 1:
        return f"Tomorrow ({event_date.strftime('%B %d, %Y')})"
    elif days_diff == -1:
        return f"Yesterday ({event_date.strftime('%B %d, %Y')})"
    elif days_diff > 0:
        return f"In {days_diff} days ({event_date.strftime('%B %d, %Y')})"
    else:
        return f"{abs(days_diff)} days ago ({event_date.strftime('%B %d, %Y')})"

def get_event_status_color(status):
    """Get color for event status"""
    colors = {
        'open': 'success',
        'closed': 'warning', 
        'completed': 'gray'
    }
    return colors.get(status, 'gray')

def can_modify_registration(registration_deadline):
    """Check if registration can be modified"""
    if not registration_deadline:
        return True
    return registration_deadline >= date.today()

def show_events_dashboard(user_email, role):
    """Show the main events dashboard"""
    create_page_header("🎉 Events Management", "Discover, register, and manage events", "🎉")

    # Get events and user registrations
    all_events = get_all_events()
    user_registrations = get_user_registrations(user_email)

    # Create metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        total_events = len(all_events)
        st.markdown(create_metric_card(total_events, "Total Events", "🎉"), unsafe_allow_html=True)

    with col2:
        open_events = len([e for e in all_events if e['status'] == 'open'])
        st.markdown(create_metric_card(open_events, "Open for Registration", "🔓", "success"), unsafe_allow_html=True)

    with col3:
        user_registered = len(user_registrations)
        st.markdown(create_metric_card(user_registered, "Your Registrations", "👤", "primary"), unsafe_allow_html=True)

    with col4:
        upcoming_events = len([e for e in all_events if e['event_date'] >= date.today()])
        st.markdown(create_metric_card(upcoming_events, "Upcoming Events", "📅", "warning"), unsafe_allow_html=True)

    st.markdown("<div class='section-spacing'></div>", unsafe_allow_html=True)

    # Create tabs based on role
    if role == "admin":
        tabs = st.tabs(["📋 All Events", "👤 My Registrations", "➕ Create Event", "⚙️ Manage Events"])

        with tabs[0]:
            show_all_events_tab(user_email, all_events)

        with tabs[1]:
            show_user_registrations_tab(user_email, user_registrations)

        with tabs[2]:
            show_create_event_tab()

        with tabs[3]:
            show_admin_manage_events_tab(all_events)

    else:
        tabs = st.tabs(["📋 All Events", "👤 My Registrations"])

        with tabs[0]:
            show_all_events_tab(user_email, all_events)

        with tabs[1]:
            show_user_registrations_tab(user_email, user_registrations)

def show_all_events_tab(user_email, events):
    """Show all events with enhanced presentation"""
    try:
        from enhanced_presentation import show_enhanced_all_events_tab
        show_enhanced_all_events_tab(user_email, events)
    except ImportError:
        # Fallback to original implementation
        show_original_all_events_tab(user_email, events)

def show_original_all_events_tab(user_email, events):
    """Original all events display (fallback)"""
    st.markdown("### 📋 All Events")

    if not events:
        show_alert("No events available at the moment.", "info")
        return

    # Filter options
    col1, col2, col3 = st.columns(3)

    with col1:
        status_filter = st.selectbox(
            "Filter by Status",
            ["All", "Open", "Closed", "Completed"],
            key="event_status_filter"
        )

    with col2:
        date_filter = st.selectbox(
            "Filter by Date",
            ["All", "Upcoming", "Past", "This Month"],
            key="event_date_filter"
        )

    with col3:
        search_term = st.text_input("Search Events", placeholder="Search by title or description")

    # Apply filters
    filtered_events = events

    if status_filter != "All":
        filtered_events = [e for e in filtered_events if e['status'] == status_filter.lower()]

    if date_filter == "Upcoming":
        filtered_events = [e for e in filtered_events if e['event_date'] >= date.today()]
    elif date_filter == "Past":
        filtered_events = [e for e in filtered_events if e['event_date'] < date.today()]
    elif date_filter == "This Month":
        today = date.today()
        start_month = today.replace(day=1)
        if today.month == 12:
            end_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        filtered_events = [e for e in filtered_events if start_month <= e['event_date'] <= end_month]

    if search_term:
        search_lower = search_term.lower()
        filtered_events = [
            e for e in filtered_events
            if search_lower in e['title'].lower() or search_lower in (e['description'] or '').lower()
        ]

    st.markdown(f"**Showing {len(filtered_events)} of {len(events)} events**")

    # Display events
    for event in filtered_events:
        show_event_card(user_email, event)

def show_event_card(user_email, event):
    """Display a single event card"""
    # Check if user is registered
    user_id = get_user_id_by_email(user_email)
    is_registered = False

    if user_id:
        try:
            conn = get_connection()
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT id FROM event_participants
                    WHERE user_id = %s AND event_id = %s
                """, (user_id, event['id']))
                is_registered = cur.fetchone() is not None
            conn.close()
        except:
            pass

    # Create event card
    with st.container():
        st.markdown(f"""
        <div class="professional-card">
            <div class="card-header">
                <h3 class="card-title">{event['title']}</h3>
                <div>
                    {create_status_badge(event['status'])}
                    {create_status_badge('registered', '✓ Registered') if is_registered else ''}
                </div>
            </div>
            <div class="card-content">
                <p><strong>📅 Date:</strong> {format_event_date(event['event_date'])}</p>
                <p><strong>📍 Location:</strong> {event['location'] or 'TBA'}</p>
                <p><strong>👥 Registrations:</strong> {event['registration_count']}{f"/{event['max_participants']}" if event['max_participants'] else ""}</p>
                {f"<p><strong>⏰ Registration Deadline:</strong> {event['registration_deadline'].strftime('%B %d, %Y') if event['registration_deadline'] else 'No deadline'}</p>" if event.get('registration_deadline') else ""}
                <p><strong>📝 Description:</strong> {event['description'] or 'No description available'}</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Action buttons
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            if not is_registered and event['status'] == 'open':
                if st.button("📝 Register", key=f"register_{event['id']}"):
                    success, message = register_for_event(user_email, event['id'])
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)

        with col2:
            if is_registered and can_modify_registration(event.get('registration_deadline')):
                if st.button("❌ Cancel Registration", key=f"cancel_{event['id']}"):
                    success, message = cancel_registration(user_email, event['id'])
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)

        with col3:
            if st.button("👥 View Participants", key=f"participants_{event['id']}"):
                st.session_state[f"show_participants_{event['id']}"] = True

        with col4:
            if st.button("ℹ️ More Details", key=f"details_{event['id']}"):
                st.session_state[f"show_details_{event['id']}"] = True

        # Show participants if requested
        if st.session_state.get(f"show_participants_{event['id']}", False):
            show_event_participants(event['id'], event['title'])

        # Show details if requested
        if st.session_state.get(f"show_details_{event['id']}", False):
            show_event_details(event)

        st.markdown("---")

def show_event_participants(event_id, event_title):
    """Show event participants"""
    participants = get_event_participants(event_id)

    with st.expander(f"👥 Participants for {event_title} ({len(participants)} registered)", expanded=True):
        if participants:
            # Create DataFrame for better display
            df = pd.DataFrame(participants)
            df['registered_at'] = pd.to_datetime(df['registered_at']).dt.strftime('%Y-%m-%d %H:%M')

            # Display as table
            st.dataframe(
                df[['firstname', 'lastname', 'email', 'department', 'role', 'registered_at']],
                column_config={
                    'firstname': 'First Name',
                    'lastname': 'Last Name',
                    'email': 'Email',
                    'department': 'Department',
                    'role': 'Role',
                    'registered_at': 'Registered At'
                },
                hide_index=True,
                use_container_width=True
            )
        else:
            st.info("No participants registered yet.")

        if st.button("Close", key=f"close_participants_{event_id}"):
            st.session_state[f"show_participants_{event_id}"] = False
            st.rerun()

def show_event_details(event):
    """Show detailed event information"""
    with st.expander(f"ℹ️ Details for {event['title']}", expanded=True):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown(f"""
            **📅 Event Date:** {format_event_date(event['event_date'])}

            **📍 Location:** {event['location'] or 'To be announced'}

            **👥 Current Registrations:** {event['registration_count']}

            **🎯 Maximum Participants:** {event['max_participants'] or 'Unlimited'}
            """)

        with col2:
            st.markdown(f"""
            **⏰ Registration Deadline:** {event['registration_deadline'].strftime('%B %d, %Y') if event['registration_deadline'] else 'No deadline'}

            **📊 Status:** {event['status'].title()}

            **📅 Created:** {event['created_at'].strftime('%B %d, %Y') if event['created_at'] else 'Unknown'}

            **🆔 Event ID:** {event['id']}
            """)

        st.markdown("**📝 Full Description:**")
        st.markdown(event['description'] or 'No description available.')

        if st.button("Close Details", key=f"close_details_{event['id']}"):
            st.session_state[f"show_details_{event['id']}"] = False
            st.rerun()

def show_user_registrations_tab(user_email, registrations):
    """Show user's event registrations"""
    st.markdown("### 👤 My Event Registrations")

    if not registrations:
        show_alert("You haven't registered for any events yet. Browse the 'All Events' tab to find interesting events!", "info")
        return

    # Categorize registrations
    upcoming = [r for r in registrations if r['event_date'] >= date.today()]
    past = [r for r in registrations if r['event_date'] < date.today()]

    # Show summary
    col1, col2 = st.columns(2)
    with col1:
        st.markdown(create_metric_card(len(upcoming), "Upcoming Events", "📅", "primary"), unsafe_allow_html=True)
    with col2:
        st.markdown(create_metric_card(len(past), "Past Events", "📚", "gray"), unsafe_allow_html=True)

    # Show upcoming registrations
    if upcoming:
        st.markdown("#### 📅 Upcoming Events")
        for registration in upcoming:
            show_registration_card(user_email, registration)

    # Show past registrations
    if past:
        st.markdown("#### 📚 Past Events")
        for registration in past:
            show_registration_card(user_email, registration, is_past=True)

def show_registration_card(user_email, registration, is_past=False):
    """Display a registration card"""
    can_modify = can_modify_registration(registration.get('registration_deadline')) and not is_past

    with st.container():
        st.markdown(f"""
        <div class="professional-card">
            <div class="card-header">
                <h3 class="card-title">{registration['title']}</h3>
                <div>
                    {create_status_badge(registration['event_status'])}
                    {create_status_badge('past', '📚 Past Event') if is_past else ''}
                </div>
            </div>
            <div class="card-content">
                <p><strong>📅 Event Date:</strong> {format_event_date(registration['event_date'])}</p>
                <p><strong>📍 Location:</strong> {registration['location'] or 'TBA'}</p>
                <p><strong>📝 Registered:</strong> {registration['registered_at'].strftime('%B %d, %Y at %I:%M %p')}</p>
                <p><strong>📊 Status:</strong> {registration['registration_status'].title()}</p>
                {f"<p><strong>⏰ Registration Deadline:</strong> {registration['registration_deadline'].strftime('%B %d, %Y')}</p>" if registration.get('registration_deadline') else ""}
                <p><strong>📝 Description:</strong> {registration['description'] or 'No description available'}</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Action buttons
        col1, col2, col3 = st.columns(3)

        with col1:
            if can_modify:
                if st.button("❌ Cancel Registration", key=f"cancel_reg_{registration['id']}"):
                    success, message = cancel_registration(user_email, registration['id'])
                    if success:
                        st.success(message)
                        st.rerun()
                    else:
                        st.error(message)
            elif not is_past:
                st.info("Cannot modify after deadline")

        with col2:
            if st.button("👥 View Participants", key=f"view_participants_{registration['id']}"):
                st.session_state[f"show_participants_{registration['id']}"] = True

        with col3:
            if st.button("📧 Contact Organizer", key=f"contact_{registration['id']}"):
                st.session_state[f"contact_organizer_{registration['id']}"] = True

        # Show participants if requested
        if st.session_state.get(f"show_participants_{registration['id']}", False):
            show_event_participants(registration['id'], registration['title'])

        # Show contact organizer form if requested
        if st.session_state.get(f"contact_organizer_{registration['id']}", False):
            with st.expander(f"📧 Contact Organizer - {registration['title']}", expanded=True):
                from communication_system import show_contact_organizer_form
                show_contact_organizer_form(
                    registration['id'],
                    registration['title'],
                    st.session_state.get('user_email')
                )

                if st.button("Close", key=f"close_contact_{registration['id']}"):
                    del st.session_state[f"contact_organizer_{registration['id']}"]
                    st.rerun()

        st.markdown("---")

def show_create_event_tab():
    """Show create event form for admins"""
    st.markdown("### ➕ Create New Event")

    with st.form("create_event_form", clear_on_submit=True):
        col1, col2 = st.columns(2)

        with col1:
            title = st.text_input("Event Title *", placeholder="Enter event title")
            event_date = st.date_input("Event Date *", min_value=date.today())
            location = st.text_input("Location", placeholder="Enter event location")

        with col2:
            registration_deadline = st.date_input(
                "Registration Deadline",
                value=event_date - timedelta(days=1) if event_date else date.today(),
                max_value=event_date if event_date else None
            )
            max_participants = st.number_input(
                "Maximum Participants",
                min_value=1,
                value=50,
                help="Leave empty for unlimited"
            )

            # Option to make unlimited
            unlimited = st.checkbox("Unlimited participants")
            if unlimited:
                max_participants = None

        description = st.text_area(
            "Event Description *",
            placeholder="Describe the event, agenda, requirements, etc.",
            height=150
        )

        submitted = st.form_submit_button("🎉 Create Event", use_container_width=True)

        if submitted:
            if not title or not event_date or not description:
                st.error("Please fill in all required fields (marked with *)")
            elif registration_deadline and registration_deadline > event_date:
                st.error("Registration deadline cannot be after the event date")
            else:
                success, message = create_event(
                    title=title,
                    description=description,
                    event_date=event_date,
                    location=location,
                    registration_deadline=registration_deadline,
                    max_participants=max_participants if not unlimited else None
                )

                if success:
                    st.success(message)
                    st.balloons()
                    st.rerun()
                else:
                    st.error(message)

def show_admin_manage_events_tab(events):
    """Show admin event management interface"""
    st.markdown("### ⚙️ Manage Events")

    if not events:
        show_alert("No events to manage.", "info")
        return

    # Event selection
    event_options = [f"{event['title']} - {event['event_date']}" for event in events]
    selected_event_index = st.selectbox(
        "Select Event to Manage",
        range(len(events)),
        format_func=lambda x: event_options[x]
    )

    selected_event = events[selected_event_index]

    # Management tabs
    mgmt_tabs = st.tabs(["📊 Overview", "✏️ Edit Event", "👥 Manage Registrations", "🗑️ Delete Event"])

    with mgmt_tabs[0]:
        show_event_overview(selected_event)

    with mgmt_tabs[1]:
        show_edit_event_form(selected_event)

    with mgmt_tabs[2]:
        show_manage_registrations(selected_event)

    with mgmt_tabs[3]:
        show_delete_event_form(selected_event)

def show_event_overview(event):
    """Show event overview for admin"""
    st.markdown(f"#### 📊 Overview: {event['title']}")

    # Metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(create_metric_card(event['registration_count'], "Registrations", "👥"), unsafe_allow_html=True)

    with col2:
        capacity = event['max_participants'] or "∞"
        st.markdown(create_metric_card(capacity, "Capacity", "🎯"), unsafe_allow_html=True)

    with col3:
        days_until = (event['event_date'] - date.today()).days
        st.markdown(create_metric_card(days_until, "Days Until Event", "📅"), unsafe_allow_html=True)

    with col4:
        status_color = get_event_status_color(event['status'])
        st.markdown(create_metric_card(event['status'].title(), "Status", "📊", status_color), unsafe_allow_html=True)

    # Event details
    st.markdown("#### Event Details")
    col1, col2 = st.columns(2)

    with col1:
        st.markdown(f"""
        **📅 Event Date:** {format_event_date(event['event_date'])}
        **📍 Location:** {event['location'] or 'TBA'}
        **⏰ Registration Deadline:** {event['registration_deadline'].strftime('%B %d, %Y') if event['registration_deadline'] else 'No deadline'}
        """)

    with col2:
        st.markdown(f"""
        **🎯 Max Participants:** {event['max_participants'] or 'Unlimited'}
        **📊 Current Status:** {event['status'].title()}
        **📅 Created:** {event['created_at'].strftime('%B %d, %Y') if event['created_at'] else 'Unknown'}
        """)

    st.markdown("**📝 Description:**")
    st.markdown(event['description'] or 'No description available.')

    # Recent registrations
    participants = get_event_participants(event['id'])
    if participants:
        st.markdown("#### 👥 Recent Registrations")
        recent_participants = sorted(participants, key=lambda x: x['registered_at'], reverse=True)[:5]

        for participant in recent_participants:
            st.markdown(f"• **{participant['firstname']} {participant['lastname']}** ({participant['email']}) - {participant['registered_at'].strftime('%B %d, %Y')}")

        if len(participants) > 5:
            st.markdown(f"*... and {len(participants) - 5} more participants*")

def show_edit_event_form(event):
    """Show edit event form"""
    st.markdown(f"#### ✏️ Edit Event: {event['title']}")

    with st.form("edit_event_form"):
        col1, col2 = st.columns(2)

        with col1:
            title = st.text_input("Event Title", value=event['title'])
            event_date = st.date_input("Event Date", value=event['event_date'])
            location = st.text_input("Location", value=event['location'] or "")

        with col2:
            registration_deadline = st.date_input(
                "Registration Deadline",
                value=event['registration_deadline'] or event['event_date']
            )
            max_participants = st.number_input(
                "Maximum Participants",
                min_value=1,
                value=event['max_participants'] or 50
            )

            unlimited = st.checkbox("Unlimited participants", value=event['max_participants'] is None)

        description = st.text_area(
            "Event Description",
            value=event['description'] or "",
            height=150
        )

        submitted = st.form_submit_button("💾 Update Event", use_container_width=True)

        if submitted:
            if not title or not event_date or not description:
                st.error("Please fill in all required fields")
            else:
                success, message = update_event(
                    event_id=event['id'],
                    title=title,
                    description=description,
                    event_date=event_date,
                    location=location,
                    registration_deadline=registration_deadline,
                    max_participants=max_participants if not unlimited else None
                )

                if success:
                    st.success(message)
                    st.rerun()
                else:
                    st.error(message)

def show_manage_registrations(event):
    """Show registration management for admin"""
    st.markdown(f"#### 👥 Manage Registrations: {event['title']}")

    participants = get_event_participants(event['id'])

    if not participants:
        show_alert("No registrations for this event yet.", "info")
        return

    st.markdown(f"**Total Registrations: {len(participants)}**")

    # Create DataFrame for better management
    df = pd.DataFrame(participants)
    df['registered_at'] = pd.to_datetime(df['registered_at']).dt.strftime('%Y-%m-%d %H:%M')

    # Display participants with management options
    for i, participant in enumerate(participants):
        with st.expander(f"{participant['firstname']} {participant['lastname']} ({participant['email']})"):
            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown(f"""
                **Name:** {participant['firstname']} {participant['lastname']}
                **Email:** {participant['email']}
                **Department:** {participant['department']}
                """)

            with col2:
                st.markdown(f"""
                **Role:** {participant['role']}
                **Registered:** {participant['registered_at'].strftime('%B %d, %Y at %I:%M %p')}
                **Status:** {participant['status'].title()}
                """)

            with col3:
                if st.button("📧 Send Email", key=f"email_{event['id']}_{i}"):
                    st.session_state[f"email_participant_{event['id']}_{participant['user_id']}"] = True

                if st.button("❌ Remove Registration", key=f"remove_{event['id']}_{i}"):
                    st.session_state[f"remove_participant_{event['id']}_{participant['user_id']}"] = True

    # Export options
    st.markdown("#### 📊 Export Options")
    col1, col2 = st.columns(2)

    with col1:
        if st.button("📄 Export to CSV"):
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download CSV",
                data=csv,
                file_name=f"event_{event['id']}_participants.csv",
                mime="text/csv"
            )

    with col2:
        if st.button("📧 Email All Participants"):
            st.session_state[f"bulk_email_{event['id']}"] = True

    # Show bulk email form if requested
    if st.session_state.get(f"bulk_email_{event['id']}", False):
        with st.expander("📧 Email All Participants", expanded=True):
            from communication_system import show_participant_email_form
            show_participant_email_form(
                event['id'],
                event['title'],
                st.session_state.get('user_email')
            )

            if st.button("Close Email Form", key=f"close_bulk_email_{event['id']}"):
                del st.session_state[f"bulk_email_{event['id']}"]
                st.rerun()

    # Handle individual participant actions
    for key in list(st.session_state.keys()):
        if key.startswith(f"email_participant_{event['id']}_"):
            user_id = key.split('_')[-1]
            participant = next((p for p in participants if str(p['user_id']) == user_id), None)

            if participant:
                with st.expander(f"📧 Email {participant['firstname']} {participant['lastname']}", expanded=True):
                    from communication_system import send_internal_message

                    with st.form(f"email_form_{user_id}"):
                        subject = st.text_input("Subject", value=f"Regarding {event['title']}")
                        message = st.text_area("Message", height=100)

                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("Send Email"):
                                if subject and message:
                                    success, result = send_internal_message(
                                        st.session_state.get('user_email'),
                                        participant['email'],
                                        subject,
                                        message,
                                        "event_notification",
                                        event['id']
                                    )
                                    if success:
                                        st.success("Email sent!")
                                        del st.session_state[key]
                                        st.rerun()
                                    else:
                                        st.error(result)
                                else:
                                    st.error("Subject and message required")

                        with col2:
                            if st.form_submit_button("Cancel"):
                                del st.session_state[key]
                                st.rerun()

        elif key.startswith(f"remove_participant_{event['id']}_"):
            user_id = key.split('_')[-1]
            participant = next((p for p in participants if str(p['user_id']) == user_id), None)

            if participant:
                with st.expander(f"🗑️ Remove {participant['firstname']} {participant['lastname']}", expanded=True):
                    st.warning("⚠️ This will permanently remove this registration!")

                    reason = st.text_area("Reason for removal:", height=80)

                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("Confirm Removal", key=f"confirm_{user_id}"):
                            from admin_management import remove_user_registration
                            success, message = remove_user_registration(
                                event['id'],
                                int(user_id),
                                st.session_state.get('user_email'),
                                reason
                            )
                            if success:
                                st.success(message)
                                del st.session_state[key]
                                st.rerun()
                            else:
                                st.error(message)

                    with col2:
                        if st.button("Cancel", key=f"cancel_{user_id}"):
                            del st.session_state[key]
                            st.rerun()

def show_delete_event_form(event):
    """Show delete event form with confirmation"""
    st.markdown(f"#### 🗑️ Delete Event: {event['title']}")

    st.warning("⚠️ **Warning:** Deleting an event will permanently remove it and all associated registrations. This action cannot be undone.")

    # Show event info
    st.markdown(f"""
    **Event:** {event['title']}
    **Date:** {format_event_date(event['event_date'])}
    **Registrations:** {event['registration_count']} participants
    **Status:** {event['status'].title()}
    """)

    # Confirmation
    confirm_text = st.text_input(
        f"Type '{event['title']}' to confirm deletion:",
        placeholder=f"Enter: {event['title']}"
    )

    if confirm_text == event['title']:
        if st.button("🗑️ DELETE EVENT", key=f"delete_confirm_{event['id']}"):
            success, message = delete_event(event['id'])
            if success:
                st.success(message)
                st.rerun()
            else:
                st.error(message)
    else:
        st.button("🗑️ DELETE EVENT", disabled=True, help="Type the event title to enable deletion")
