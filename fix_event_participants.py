#!/usr/bin/env python3
"""
Script to fix event_participants table structure
"""

from db import get_connection
import psycopg2.extras

def fix_event_participants_table():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        # Check current structure
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'event_participants'
            ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print('EVENT_PARTICIPANTS table structure:')
        column_names = []
        for col in columns:
            column_names.append(col['column_name'])
            print(f'  {col["column_name"]} ({col["data_type"]})')
        
        # Add missing columns
        missing_columns = []
        
        if 'registered_at' not in column_names:
            missing_columns.append("registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            
        if 'status' not in column_names:
            missing_columns.append("status VARCHAR(20) DEFAULT 'registered'")
        
        # Add missing columns
        if missing_columns:
            print(f"\nAdding missing columns: {missing_columns}")
            for column in missing_columns:
                try:
                    cur.execute(f"ALTER TABLE event_participants ADD COLUMN {column}")
                    print(f"Added column: {column}")
                except Exception as e:
                    print(f"Error adding column {column}: {e}")
            
            conn.commit()
            print("Missing columns added successfully!")
        else:
            print("\nAll required columns exist.")
        
        # Test the table
        cur.execute("SELECT COUNT(*) as count FROM event_participants")
        count = cur.fetchone()['count']
        print(f"\nEvent participants table has {count} records")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    fix_event_participants_table()
