#!/usr/bin/env python3
"""
Script to check user interests and current recommendation system
"""

from db import get_connection
import psycopg2.extras

def check_user_interests():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Check users table structure for interests
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name LIKE '%interest%'
        """)
        interest_columns = cur.fetchall()
        print('Interest-related columns in users table:')
        for col in interest_columns:
            print(f'  {col["column_name"]} ({col["data_type"]})')

        # Check sample user data
        cur.execute('SELECT email, interests, department, role FROM users LIMIT 5')
        users = cur.fetchall()
        print('\nSample user data:')
        for user in users:
            interests = user.get('interests', 'None')
            print(f'  {user["email"]}: {interests} (Dept: {user["department"]}, Role: {user["role"]})')

        # Check if data and models directories exist
        import os
        print(f'\nDirectory structure:')
        print(f'  data/ exists: {os.path.exists("data")}')
        print(f'  models/ exists: {os.path.exists("models")}')
        
        if os.path.exists("data"):
            data_files = os.listdir("data")
            print(f'  Files in data/: {data_files}')
        
        if os.path.exists("models"):
            model_files = os.listdir("models")
            print(f'  Files in models/: {model_files}')

        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_user_interests()
