#!/usr/bin/env python3
"""
Script to check user table schema and add missing columns if needed
"""

from db import get_connection
import psycopg2.extras

def check_user_schema():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

        # Check users table structure
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'users'
            ORDER BY ordinal_position
        """)
        columns = cur.fetchall()
        print('USERS table structure:')
        column_names = []
        for col in columns:
            column_names.append(col['column_name'])
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f'  {col["column_name"]} ({col["data_type"]}) - Nullable: {col["is_nullable"]}{default}')

        # Check if we need to add status and confirmation columns
        missing_columns = []
        
        if 'is_active' not in column_names:
            missing_columns.append("is_active BOOLEAN DEFAULT TRUE")
            
        if 'is_confirmed' not in column_names:
            missing_columns.append("is_confirmed BOOLEAN DEFAULT FALSE")
            
        if 'confirmed_by' not in column_names:
            missing_columns.append("confirmed_by VARCHAR(255)")
            
        if 'confirmed_at' not in column_names:
            missing_columns.append("confirmed_at TIMESTAMP")
            
        if 'created_at' not in column_names:
            missing_columns.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            
        if 'updated_at' not in column_names:
            missing_columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")

        # Add missing columns
        if missing_columns:
            print(f"\nAdding missing columns: {missing_columns}")
            for column in missing_columns:
                try:
                    cur.execute(f"ALTER TABLE users ADD COLUMN {column}")
                    print(f"Added column: {column}")
                except Exception as e:
                    print(f"Error adding column {column}: {e}")
            
            conn.commit()
            print("Missing columns added successfully!")
        else:
            print("\nAll required columns exist.")

        # Check sample data
        cur.execute("SELECT email, role, is_active, is_confirmed FROM users LIMIT 5")
        sample_users = cur.fetchall()
        print("\nSample user data:")
        for user in sample_users:
            print(f"  {user['email']} ({user['role']}) - Active: {user.get('is_active', 'N/A')}, Confirmed: {user.get('is_confirmed', 'N/A')}")

        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_user_schema()
