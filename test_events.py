#!/usr/bin/env python3
"""
Script to test events management functionality
"""

from db import get_connection
import psycopg2.extras
from datetime import datetime, timedelta

def test_events_functionality():
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print("Testing events management...")
        
        # Test creating new events
        future_date = datetime.now() + timedelta(days=30)
        
        new_events = [
            ("Alumni Networking Night", "Join us for an evening of networking and reconnecting with fellow alumni.", 
             future_date.strftime("%Y-%m-%d"), "University Main Hall"),
            ("Career Development Workshop", "Learn about the latest trends in your industry and career advancement strategies.", 
             (future_date + timedelta(days=15)).strftime("%Y-%m-%d"), "Conference Room A"),
            ("Alumni Sports Day", "Fun sports activities and competitions for all alumni and their families.", 
             (future_date + timedelta(days=45)).strftime("%Y-%m-%d"), "University Sports Complex")
        ]
        
        for title, description, event_date, location in new_events:
            cur.execute("""
                INSERT INTO events (title, description, event_date, location, created_at)
                VALUES (%s, %s, %s, %s, NOW())
            """, (title, description, event_date, location))
            print(f"  ✅ Created event: {title}")
        
        conn.commit()
        
        # Test retrieving events
        cur.execute("""
            SELECT id, title, description, event_date, location, created_at
            FROM events
            ORDER BY event_date ASC
        """)
        
        events = cur.fetchall()
        print(f"\nRetrieved {len(events)} events:")
        
        for event in events:
            print(f"  {event['title']} - {event['event_date']} at {event['location']}")
            print(f"    \"{event['description'][:50]}...\"")
        
        # Test event registration
        print(f"\nTesting event registrations...")
        
        # Get user IDs
        cur.execute("SELECT id, email FROM users")
        users = cur.fetchall()
        
        # Register users for events
        event_ids = [event['id'] for event in events]
        
        for i, user in enumerate(users):
            event_id = event_ids[i % len(event_ids)]
            
            cur.execute("""
                INSERT INTO event_participants (event_id, user_id, registered_at)
                VALUES (%s, %s, NOW())
                ON CONFLICT (event_id, user_id) DO NOTHING
            """, (event_id, user['id']))
            
            print(f"  ✅ Registered {user['email']} for event ID {event_id}")
        
        conn.commit()
        
        # Test event analytics
        cur.execute("""
            SELECT e.title, COUNT(ep.user_id) as participant_count
            FROM events e
            LEFT JOIN event_participants ep ON e.id = ep.event_id
            GROUP BY e.id, e.title
            ORDER BY participant_count DESC
        """)
        
        event_stats = cur.fetchall()
        print(f"\nEvent participation statistics:")
        
        for stat in event_stats:
            print(f"  {stat['title']}: {stat['participant_count']} participants")
        
        # Test upcoming events
        cur.execute("""
            SELECT title, event_date, location
            FROM events
            WHERE event_date >= CURRENT_DATE
            ORDER BY event_date ASC
            LIMIT 3
        """)
        
        upcoming_events = cur.fetchall()
        print(f"\nUpcoming events:")
        
        for event in upcoming_events:
            print(f"  {event['title']} - {event['event_date']} at {event['location']}")
        
        conn.close()
        print("\nEvents management test completed successfully!")
        
    except Exception as e:
        print(f"Error testing events: {e}")

if __name__ == "__main__":
    test_events_functionality()
