import streamlit as st
from streamlit_option_menu import option_menu  # You'll need to install this package
from PIL import Image
from db import get_connection, authenticate_user, get_user_permissions  # Ensure this is imported to use the correct connection function
import psycopg2  # PostgreSQL library
from psycopg2.extras import RealDict<PERSON>ursor  # For fetching rows as dictionaries
import bcrypt  # Add this import for password hashing and verification
import pandas as pd  # Add this import for handling CSV data
import os
import joblib  # For saving and loading models
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score
import threading  # For running background tasks
import time  # Add this import at the top of the file
from datetime import datetime, date

# Import professional UI components and event management
try:
    from ui_components import load_professional_css, create_page_header, create_metric_card, create_status_badge, show_alert
    from event_management import show_events_dashboard
    UI_COMPONENTS_AVAILABLE = True
except ImportError:
    UI_COMPONENTS_AVAILABLE = False
    print("UI components not available - using fallback styling")

def load_css():
    """Load CSS styling - use professional UI if available, fallback to basic styling"""
    if UI_COMPONENTS_AVAILABLE:
        load_professional_css()
        return

    # Fallback basic styling
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

    /* Global Styles */
    .main {
        padding: 1rem 2rem;
        font-family: 'Inter', sans-serif;
    }

    /* Modern Color Palette */
    :root {
        --primary-color: #2563eb;
        --primary-hover: #1d4ed8;
        --secondary-color: #64748b;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --background-light: #f8fafc;
        --background-card: #ffffff;
        --text-primary: #1e293b;
        --text-secondary: #64748b;
        --border-color: #e2e8f0;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* Typography */
    h1, h2, h3, h4, h5, h6 {
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1.2;
    }

    h1 {
        font-size: 2.25rem;
        margin-bottom: 1.5rem;
    }

    h2 {
        font-size: 1.875rem;
        margin-bottom: 1.25rem;
    }

    h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    h4 {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    p {
        color: var(--text-secondary);
        line-height: 1.6;
    }

    /* Button Styles */
    .stButton button {
        background: linear-gradient(135deg, #4f46e5, #3730a3);
        color: white !important;
        border: none;
        border-radius: 8px;
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        font-weight: 600;
        font-family: 'Inter', sans-serif;
        transition: all 0.2s ease-in-out;
        box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
        cursor: pointer;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .stButton button:hover {
        background: linear-gradient(135deg, #3730a3, #312e81);
        box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3);
        transform: translateY(-1px);
        color: white !important;
    }

    .stButton button:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
        color: white !important;
    }

    /* Secondary Button Style */
    .stButton.secondary button {
        background: #f8fafc !important;
        color: #374151 !important;
        border: 2px solid #d1d5db !important;
        font-weight: 600 !important;
    }

    .stButton.secondary button:hover {
        background:  !important;
        border-color: #4f46e5 !important;
        color: #4f46e5 !important;
    }

    /* Success Button Style */
    .stButton.success button {
        background: linear-gradient(135deg, #10b981, #059669) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .stButton.success button:hover {
        background: linear-gradient(135deg, #059669, #047857) !important;
        color: white !important;
    }

    /* Warning Button Style */
    .stButton.warning button {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .stButton.warning button:hover {
        background: linear-gradient(135deg, #d97706, #b45309) !important;
        color: white !important;
    }

    /* Error/Danger Button Style */
    .stButton.danger button {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .stButton.danger button:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
        color: white !important;
    }

    /* Form Submit Button Styles */
    .stForm button[type="submit"] {
        background: linear-gradient(135deg, #4f46e5, #3730a3) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 0.75rem 2rem !important;
        font-size: 1rem !important;
        font-weight: 600 !important;
        font-family: 'Inter', sans-serif !important;
        transition: all 0.2s ease-in-out !important;
        box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2) !important;
        cursor: pointer !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    .stForm button[type="submit"]:hover {
        background: linear-gradient(135deg, #3730a3, #312e81) !important;
        box-shadow: 0 4px 8px rgba(79, 70, 229, 0.3) !important;
        transform: translateY(-1px) !important;
        color: white !important;
    }

    /* Specific button overrides for better readability */
    button[kind="primary"] {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        border: none !important;
    }

    button[kind="primary"]:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
        color: white !important;
    }

    /* Logout button specific styling */
    button[data-testid="baseButton-secondary"] {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
        border: none !important;
    }

    button[data-testid="baseButton-secondary"]:hover {
        background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
        color: white !important;
    }

    /* Navigation buttons (Previous/Next) */
    .stButton > button:contains("Previous"),
    .stButton > button:contains("Next") {
        background: linear-gradient(135deg, #6366f1, #4f46e5) !important;
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    }

    .stButton > button:contains("Previous"):hover,
    .stButton > button:contains("Next"):hover {
        background: linear-gradient(135deg, #4f46e5, #3730a3) !important;
        color: white !important;
    }

    /* Override all button text colors for better readability */
    .stButton button,
    .stForm button,
    button[data-testid*="button"],
    button[kind="primary"],
    button[kind="secondary"] {
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
    }

    /* Ensure all buttons have readable text on hover */
    .stButton button:hover,
    .stForm button:hover,
    button[data-testid*="button"]:hover,
    button[kind="primary"]:hover,
    button[kind="secondary"]:hover {
        color: white !important;
    }

    /* Reset specific buttons to default Streamlit styling */
    button:contains("Train Recommendation Model"),
    button:contains("Evaluate Recommendation Models"),
    button:contains("Generate Personalized Recommendation CSV"),
    button:contains("Previous"),
    button:contains("Next"),
    button:contains("Export to CSV"),
    button[key="logout_btn"] {
        background: var(--background-color) !important;
        color: var(--text-color) !important;
        font-weight: normal !important;
        text-shadow: none !important;
        border: 1px solid var(--border-color) !important;
    }

    button:contains("Train Recommendation Model"):hover,
    button:contains("Evaluate Recommendation Models"):hover,
    button:contains("Generate Personalized Recommendation CSV"):hover,
    button:contains("Previous"):hover,
    button:contains("Next"):hover,
    button:contains("Export to CSV"):hover,
    button[key="logout_btn"]:hover {
        background: var(--background-secondary) !important;
        color: var(--text-color) !important;
    }

    /* Form Input Styles */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea,
    .stSelectbox > div > div > select,
    .stNumberInput > div > div > input {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        transition: all 0.2s ease-in-out;
        background-color: var(--background-card);
    }

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus,
    .stSelectbox > div > div > select:focus,
    .stNumberInput > div > div > input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        outline: none;
    }

    /* Card Styles */
    .custom-card {
        background: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease-in-out;
    }

    .custom-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .feature-card {
        background: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease-in-out;
        text-align: center;
    }

    .feature-card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-4px);
        border-color: var(--primary-color);
    }

    .feature-card .icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }

    .feature-card h5 {
        color: var(--text-primary);
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .feature-card p {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin: 0;
    }

    /* Sidebar Styles */
    .css-1d391kg {
        background: linear-gradient(180deg, var(--background-card) 0%, var(--background-light) 100%);
        border-right: 1px solid var(--border-color);
    }

    .css-1d391kg .css-1v0mbdj {
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    /* Navigation Menu Styles */
    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        margin-bottom: 0.25rem;
        border-radius: 8px;
        text-decoration: none;
        color: var(--text-secondary);
        transition: all 0.2s ease-in-out;
        font-weight: 500;
    }

    .nav-link:hover {
        background-color: var(--background-light);
        color: var(--primary-color);
        transform: translateX(4px);
    }

    .nav-link.active {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
        color: white;
        box-shadow: var(--shadow-md);
    }

    .nav-link .icon {
        margin-right: 0.75rem;
        font-size: 1.125rem;
    }

    /* Table Styles */
    .stDataFrame {
        border: 1px solid var(--border-color);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .stDataFrame table {
        font-family: 'Inter', sans-serif;
    }

    .stDataFrame thead th {
        background: var(--background-light);
        color: var(--text-primary);
        font-weight: 600;
        padding: 1rem;
        border-bottom: 2px solid var(--border-color);
    }

    .stDataFrame tbody td {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .stDataFrame tbody tr:hover {
        background-color: var(--background-light);
    }

    /* Tab Styles */
    .stTabs [data-baseweb="tab-list"] {
        gap: 0.5rem;
        background: var(--background-light);
        padding: 0.25rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
    }

    .stTabs [data-baseweb="tab"] {
        background: transparent;
        border-radius: 6px;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        color: var(--text-secondary);
        transition: all 0.2s ease-in-out;
    }

    .stTabs [data-baseweb="tab"]:hover {
        background: var(--background-card);
        color: var(--text-primary);
    }

    .stTabs [aria-selected="true"] {
        background: var(--background-card);
        color: var(--primary-color);
        box-shadow: var(--shadow-sm);
    }

    /* Alert Styles */
    .stAlert {
        border-radius: 8px;
        border: none;
        box-shadow: var(--shadow-sm);
    }

    .stSuccess {
        background: linear-gradient(135deg, #ecfdf5, #d1fae5);
        border-left: 4px solid var(--success-color);
        color: #065f46;
    }

    .stError {
        background: linear-gradient(135deg, #fef2f2, #fecaca);
        border-left: 4px solid var(--error-color);
        color: #991b1b;
    }

    .stWarning {
        background: linear-gradient(135deg, #fffbeb, #fef3c7);
        border-left: 4px solid var(--warning-color);
        color: #92400e;
    }

    .stInfo {
        background: linear-gradient(135deg, #eff6ff, #dbeafe);
        border-left: 4px solid var(--primary-color);
        color: #1e40af;
    }

    /* Metric Styles */
    .stMetric {
        background: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: var(--shadow-sm);
        transition: all 0.2s ease-in-out;
    }

    .stMetric:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    /* Progress Bar Styles */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
        border-radius: 4px;
    }

    /* Spinner Styles */
    .stSpinner {
        color: var(--primary-color);
    }

    /* Container Styles */
    .block-container {
        padding-top: 2rem;
        padding-bottom: 2rem;
        max-width: 1200px;
    }

    /* Hero Section */
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 3rem 2rem;
        border-radius: 16px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
        box-shadow: var(--shadow-lg);
    }

    .hero-section h1 {
        color: white;
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .hero-section p {
        color: rgba(255,255,255,0.9);
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }

    /* Login Form Styles */
    .login-container {
        background: var(--background-card);
        border: 1px solid var(--border-color);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: var(--shadow-lg);
        max-width: 400px;
        margin: 0 auto;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h3 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .login-header p {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    /* Pagination Styles */
    .pagination-container {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .pagination-info {
        background: var(--background-light);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    /* Status Badge Styles */
    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .status-badge.active {
        background: #dcfce7;
        color: #166534;
    }

    .status-badge.inactive {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-badge.pending {
        background: #fef3c7;
        color: #92400e;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .main {
            padding: 1rem;
        }

        .hero-section {
            padding: 2rem 1rem;
        }

        .hero-section h1 {
            font-size: 2rem;
        }

        .login-container {
            margin: 0 1rem;
            padding: 1.5rem;
        }

        .feature-card {
            margin-bottom: 1rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)

# Add this near the top of your file
def set_page_config():
    st.set_page_config(
        page_title="DIT Alumni Portal",
        page_icon="🎓",
        layout="wide",
        initial_sidebar_state="expanded"
    )

# Database connection setup
def get_db_connection():
    """
    Establish a connection to the PostgreSQL database.
    Replace the placeholders with actual database credentials.
    """
    return get_connection()

# Database functions
def authenticate_user(email, password):
    """
    Authenticate a user by checking their email and password in the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT password_hash FROM users WHERE email = %s", (email,))
            user = cur.fetchone()
        conn.close()

        if user and bcrypt.checkpw(password.encode(), user["password_hash"].encode()):
            return True
        return False
    except Exception as e:
        st.error(f"Database error: {e}")
        return False

def get_user_permissions(email):
    """
    Fetch the user's role/permissions from the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT role FROM users WHERE email = %s", (email,))
            result = cur.fetchone()
        conn.close()
        return [result["role"]] if result else []
    except Exception as e:
        st.error(f"Database error: {e}")
        return []

def register_user(firstname, lastname, email, password, role, department, interests, graduation_year, current_position):
    """
    Register a new user in the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO users (firstname, lastname, email, password, role, department, interests, graduation_year, current_position)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (firstname, lastname, email, password, role, department, interests, graduation_year, current_position))
        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to register user: {e}")

def get_user_by_email(email):
    """
    Fetch a user's details by email from the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT * FROM users WHERE email = %s", (email,))
            user = cur.fetchone()
        conn.close()
        return user
    except Exception as e:
        st.error(f"Database error: {e}")
        return None

def update_user_details(email, **kwargs):
    """
    Update a user's details in the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            updates = ", ".join([f"{key} = %s" for key in kwargs.keys()])
            values = list(kwargs.values()) + [email]
            query = f"UPDATE users SET {updates} WHERE email = %s"
            cur.execute(query, values)
        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to update user details: {e}")

def delete_user_by_email(email):
    """
    Delete a user by email from the PostgreSQL database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("DELETE FROM users WHERE email = %s", (email,))
        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to delete user: {e}")

def update_password_in_db(email, current_password, new_password):
    """
    Update the user's password in the PostgreSQL database after verifying the current password.
    """
    try:
        user = get_user_by_email(email)
        if not user:
            st.error("User not found.")
            return False

        # Verify the current password using bcrypt
        if not bcrypt.checkpw(current_password.encode(), user["password_hash"].encode()):
            st.error("Current password is incorrect.")
            return False

        # Hash the new password before updating
        new_password_hash = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt()).decode()
        update_user_details(email, password_hash=new_password_hash)
        return True
    except Exception as e:
        st.error(f"An error occurred while updating the password: {e}")
        return False

def update_user_status(email, is_active=None, is_confirmed=None):
    """
    Update user activation and confirmation status.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            update_fields = []
            values = []

            if is_active is not None:
                update_fields.append("is_active = %s")
                values.append(is_active)

            if is_confirmed is not None:
                update_fields.append("is_confirmed = %s")
                values.append(is_confirmed)

            if update_fields:
                update_fields.append("updated_at = CURRENT_TIMESTAMP")
                values.append(email)

                query = f"UPDATE users SET {', '.join(update_fields)} WHERE email = %s"
                cur.execute(query, values)

        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to update user status: {e}")

def confirm_user(email, admin_email):
    """
    Confirm a user account by admin.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE users
                SET is_confirmed = TRUE, confirmed_by = %s, confirmed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE email = %s
            """, (admin_email, email))

        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to confirm user: {e}")

def revoke_user_confirmation(email):
    """
    Revoke user confirmation.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE users
                SET is_confirmed = FALSE, confirmed_by = NULL, confirmed_at = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE email = %s
            """, (email,))

        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to revoke user confirmation: {e}")

def create_learning_path(interests):
    """
    Create a personalized learning path based on user interests.
    """
    learning_paths = {
        'ai': [
            {
                'title': 'Foundation in Machine Learning',
                'duration': '4-6 weeks',
                'description': 'Learn the basics of ML algorithms and data preprocessing',
                'resources': 'Andrew Ng\'s ML Course, Python for Data Science'
            },
            {
                'title': 'Deep Learning Specialization',
                'duration': '8-12 weeks',
                'description': 'Master neural networks and deep learning frameworks',
                'resources': 'Deep Learning Specialization (Coursera), TensorFlow tutorials'
            },
            {
                'title': 'AI Project Portfolio',
                'duration': '6-8 weeks',
                'description': 'Build real-world AI projects to showcase your skills',
                'resources': 'Kaggle competitions, GitHub projects, personal portfolio'
            }
        ],
        'software': [
            {
                'title': 'Programming Fundamentals',
                'duration': '6-8 weeks',
                'description': 'Master programming concepts and best practices',
                'resources': 'Clean Code book, LeetCode practice, GitHub'
            },
            {
                'title': 'Full-Stack Development',
                'duration': '10-12 weeks',
                'description': 'Learn frontend and backend development',
                'resources': 'React/Vue.js, Node.js/Django, Database design'
            },
            {
                'title': 'DevOps and Deployment',
                'duration': '4-6 weeks',
                'description': 'Learn deployment, CI/CD, and cloud platforms',
                'resources': 'Docker, AWS/Azure, Jenkins/GitHub Actions'
            }
        ],
        'business': [
            {
                'title': 'Business Strategy Fundamentals',
                'duration': '4-6 weeks',
                'description': 'Learn strategic thinking and business analysis',
                'resources': 'Harvard Business Review, Strategy courses'
            },
            {
                'title': 'Digital Marketing & Analytics',
                'duration': '6-8 weeks',
                'description': 'Master digital marketing and data-driven decisions',
                'resources': 'Google Analytics, Facebook Ads, SEO tools'
            },
            {
                'title': 'Leadership & Management',
                'duration': '8-10 weeks',
                'description': 'Develop leadership skills and team management',
                'resources': 'Leadership books, Management courses, Mentorship'
            }
        ]
    }

    # Determine which path based on interests
    if isinstance(interests, list):
        interests_str = ' '.join(interests).lower()
    else:
        interests_str = str(interests).lower()

    if any(keyword in interests_str for keyword in ['ai', 'machine learning', 'data science']):
        return learning_paths['ai']
    elif any(keyword in interests_str for keyword in ['software', 'programming', 'web development']):
        return learning_paths['software']
    elif any(keyword in interests_str for keyword in ['business', 'marketing', 'entrepreneurship']):
        return learning_paths['business']
    else:
        # Default general path
        return [
            {
                'title': 'Skill Assessment & Goal Setting',
                'duration': '1-2 weeks',
                'description': 'Identify your strengths and set clear learning goals',
                'resources': 'Self-assessment tools, Career counseling'
            },
            {
                'title': 'Core Professional Skills',
                'duration': '6-8 weeks',
                'description': 'Develop essential workplace skills',
                'resources': 'Communication, Project management, Time management'
            },
            {
                'title': 'Specialized Knowledge',
                'duration': '8-12 weeks',
                'description': 'Deep dive into your area of interest',
                'resources': 'Industry-specific courses and certifications'
            }
        ]

def generate_recommendations(user_email):
    """
    Generate personalized recommendations for a user based on their interests.
    """
    try:
        user = get_user_by_email(user_email)
        if not user or not user.get("interests"):
            return ["No recommendations available. Please update your interests."]

        interests = user["interests"]
        recommendations = []

        # Example logic for generating recommendations
        if "data science" in interests:
            recommendations.append("Recommended Course: Advanced Data Science")
        if "networking" in interests:
            recommendations.append("Recommended Event: Alumni Networking Meetup")
        if "career development" in interests:
            recommendations.append("Recommended Resource: Career Development Workshop")

        return recommendations if recommendations else ["No specific recommendations available."]
    except Exception as e:
        st.error(f"Failed to generate recommendations: {e}")
        return ["Error generating recommendations."]

def generate_recommendation_csv(user_email):
    """
    Generate a personalized CSV file for the user based on their interests.
    """
    try:
        user = get_user_by_email(user_email)
        if not user:
            st.error("User not found.")
            return None

        user_name = f"{user['firstname']}_{user['lastname']}".replace(" ", "_").lower()
        file_name = f"{user_name}_recommendations.csv"

        # Fetch user interests
        interests = user.get("interests", [])
        if not interests:
            st.error("No interests found for the user. Please update your interests.")
            return None

        # Ensure interests is a list
        if isinstance(interests, str):
            interests = [interests]

        # Get AI-based recommendations
        recommendations = predict_recommendations(user_email)
        
        # Prepare data for the CSV
        data = []
        for rec in recommendations:
            # Extract recommendation type (Course, Event, Resource)
            if "Course:" in rec:
                rec_type = "Course"
                rec_name = rec.split("Course:")[1].strip()
            elif "Event:" in rec:
                rec_type = "Event"
                rec_name = rec.split("Event:")[1].strip()
            elif "Resource:" in rec:
                rec_type = "Resource"
                rec_name = rec.split("Resource:")[1].strip()
            else:
                rec_type = "Other"
                rec_name = rec
            
            data.append([user_email, rec_name, rec_type])
        
        # If no recommendations were generated, add some based on interests
        if not data:
            interest_keywords = {
                "data science": ["Advanced Data Science", "Course"],
                "ai": ["AI Applications Workshop", "Course"],
                "machine learning": ["Machine Learning Fundamentals", "Course"],
                "networking": ["Alumni Networking Meetup", "Event"],
                "career": ["Career Development Workshop", "Resource"],
                "leadership": ["Leadership in Tech", "Course"],
                "research": ["Research Methodology", "Course"],
                "teaching": ["Teaching with AI Tools", "Resource"]
            }
            
            # Check each interest against keywords
            for interest in interests:
                interest_lower = interest.lower()
                for keyword, recommendation in interest_keywords.items():
                    if keyword in interest_lower:
                        data.append([user_email, recommendation[0], recommendation[1]])
            
            # Add default recommendations if still empty
            if not data:
                data.append([user_email, "General Career Development", "Course"])
                data.append([user_email, "Networking Basics", "Event"])
                data.append([user_email, "Professional Growth Resources", "Resource"])

        # Create a DataFrame and save it as a personalized CSV
        df = pd.DataFrame(data, columns=["User Email", "Recommendation", "Type"])
        df.to_csv(file_name, index=False)
        st.success(f"Personalized recommendation CSV '{file_name}' generated successfully!")
        
        # Return the DataFrame and filename for viewing/downloading
        return df, file_name
    except Exception as e:
        st.error(f"Failed to generate recommendation CSV: {e}")
        return None

def train_and_select_best_model():
    """
    Train three ML models and select the best one based on accuracy.
    """
    try:
        # Load training data from train_recommendation.csv in the data folder
        data_path = os.path.join("data", "train_recommendation.csv")
        if not os.path.exists(data_path):
            st.error("Training data not found. Please ensure 'train_recommendation.csv' exists in the 'data' folder.")
            return None

        df = pd.read_csv(data_path)

        # Check if required columns exist
        if "Recommendation" not in df.columns or "Type" not in df.columns:  # Ensure 'Type' column is checked
            st.error("Training data is missing required columns: 'Recommendation' and 'Type'.")
            return None

        # Ensure 'Type' column is not empty
        if df["Type"].isnull().all():
            st.error("The 'Type' column is empty. Please check the training data.")
            return None

        X = df["Recommendation"]
        y = df["Type"]  # Use the 'Type' column for labels

        # Convert text data to feature vectors
        vectorizer = CountVectorizer()
        X_vectorized = vectorizer.fit_transform(X)

        # Split data into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(X_vectorized, y, test_size=0.2, random_state=42)

        # Train models
        models = {
            "Naive Bayes": MultinomialNB(),
            "Decision Tree": DecisionTreeClassifier(),
            "Random Forest": RandomForestClassifier()
        }
        accuracies = {}

        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracies[name] = accuracy_score(y_test, y_pred)

        # Select the best model
        best_model_name = max(accuracies, key=accuracies.get)
        best_model = models[best_model_name]

        # Save the best model and vectorizer
        model_path = os.path.join("models", "best_recommendation_model.pkl")
        vectorizer_path = os.path.join("models", "vectorizer.pkl")
        os.makedirs("models", exist_ok=True)
        joblib.dump(best_model, model_path)
        joblib.dump(vectorizer, vectorizer_path)

        st.success(f"Best model '{best_model_name}' trained and saved with accuracy: {accuracies[best_model_name]:.2f}")
        return best_model, vectorizer
    except Exception as e:
        st.error(f"Failed to train models: {e}")
        return None

def predict_recommendations(user_email):
    """
    Predict recommendations for a user based on their interests using the best trained model.
    """
    try:
        # Load the best model and vectorizer
        model_path = os.path.join("models", "best_recommendation_model.pkl")
        vectorizer_path = os.path.join("models", "vectorizer.pkl")
        
        # Check if model exists, if not, train it
        if not os.path.exists(model_path) or not os.path.exists(vectorizer_path):
            st.info("Training recommendation model first...")
            result = train_and_select_best_model()
            if not result:
                return ["Could not generate recommendations. Please try again later."]
            model, vectorizer = result
        else:
            model = joblib.load(model_path)
            vectorizer = joblib.load(vectorizer_path)

        # Fetch user interests
        user = get_user_by_email(user_email)
        if not user or not user.get("interests"):
            return ["No recommendations available. Please update your interests."]

        interests = user["interests"]
        if isinstance(interests, list):  # Convert list of interests to a single string
            interests = ", ".join(interests)

        # Transform user interests into feature vectors
        interests_vectorized = vectorizer.transform([interests])

        # Predict recommendations
        predictions = model.predict(interests_vectorized)
        recommendations = []

        # Map predictions to meaningful recommendations
        for pred in predictions:
            if pred == "student":
                recommendations.append("Recommended Course: Advanced Data Science")
                recommendations.append("Recommended Event: Tech Career Fair")
                recommendations.append("Recommended Resource: ML Career Kickstart Guide")
            elif pred == "alumni":
                recommendations.append("Recommended Event: Alumni Networking Meetup")
                recommendations.append("Recommended Resource: Alumni Mentorship Toolkit")
                recommendations.append("Recommended Course: Leadership in Tech")
            elif pred == "staff":
                recommendations.append("Recommended Event: Staff Professional Summit")
                recommendations.append("Recommended Resource: Innovative Teaching Handbook")
                recommendations.append("Recommended Course: Teaching with AI Tools")
            else:
                # Default recommendations if prediction doesn't match expected categories
                recommendations.append(f"Recommended based on your interests: {pred}")

        return recommendations if recommendations else ["No specific recommendations available."]
    except Exception as e:
        st.error(f"Failed to generate predictions: {e}")
        return ["Error generating recommendations. Please try again later."]

# Session state setup
if "authenticated" not in st.session_state:
    st.session_state.authenticated = False
if "page" not in st.session_state:
    st.session_state.page = "landing"

def set_page(page_name):
    st.session_state.page = page_name  # Update the current page in session state

def show_landing_page():
    # Check for special pages
    if st.session_state.get('show_password_reset', False):
        from auth_system import show_password_reset_form
        show_password_reset_form()

        if st.button("← Back to Login"):
            st.session_state['show_password_reset'] = False
            st.rerun()
        return

    if st.session_state.get('show_user_registration', False):
        from auth_system import show_user_registration_form
        show_user_registration_form()

        if st.button("← Back to Login"):
            st.session_state['show_user_registration'] = False
            st.rerun()
        return

    # Modern Hero Section
    st.markdown("""
    <div class="hero-section">
        <h1>🎓 DIT Alumni Portal</h1>
        <p>Connect, collaborate, and grow your future with DIT alumni community</p>
        <div style="display: flex; justify-content: center; gap: 1rem; margin-top: 2rem;">
            <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; backdrop-filter: blur(10px);">
                <span style="font-weight: 600;">1000+</span> Alumni
            </div>
            <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; backdrop-filter: blur(10px);">
                <span style="font-weight: 600;">50+</span> Companies
            </div>
            <div style="background: rgba(255,255,255,0.2); padding: 0.5rem 1rem; border-radius: 20px; backdrop-filter: blur(10px);">
                <span style="font-weight: 600;">25+</span> Countries
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Create two columns with better spacing
    col1, col2 = st.columns([3, 2], gap="large")

    # Left column - Enhanced feature cards
    with col1:
        st.markdown("### Why Join Our Community?")

        # Feature cards with modern design
        st.markdown("""
        <div class="feature-card">
            <div class="icon">🔗</div>
            <h5>Stay Connected</h5>
            <p>Reunite with classmates and build lasting professional relationships that span across industries and continents.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="feature-card">
            <div class="icon">🤝</div>
            <h5>Collaborate & Share</h5>
            <p>Exchange ideas, share opportunities, and collaborate on projects with fellow alumni from diverse backgrounds.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="feature-card">
            <div class="icon">📈</div>
            <h5>Professional Growth</h5>
            <p>Access exclusive resources, mentorship programs, and career development opportunities tailored for DIT alumni.</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("""
        <div class="feature-card">
            <div class="icon">🎯</div>
            <h5>AI-Powered Recommendations</h5>
            <p>Get personalized career recommendations and networking suggestions powered by advanced AI algorithms.</p>
        </div>
        """, unsafe_allow_html=True)

    # Right column - Enhanced Login Form
    with col2:
        st.markdown("""
        <div class="login-container">
            <div class="login-header">
                <h3>🔐 Welcome Back</h3>
                <p>Sign in to access your alumni portal</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

        with st.form("login_form", clear_on_submit=False):
            st.markdown("##### Login to Your Account")

            # Enhanced form fields
            email = st.text_input(
                "Email Address",
                placeholder="Enter your email address",
                help="Use the email address associated with your alumni account"
            )
            password = st.text_input(
                "Password",
                type="password",
                placeholder="Enter your password",
                help="Enter your secure password"
            )

            # Remember me checkbox
            remember_me = st.checkbox("Remember me for 30 days")

            # Login button with enhanced styling
            st.markdown('<div style="margin-top: 1.5rem;"></div>', unsafe_allow_html=True)
            login_btn = st.form_submit_button("Sign In", use_container_width=True)

            if login_btn:
                if authenticate_user(email, password):
                    st.session_state.authenticated = True
                    st.session_state.user_email = email
                    if remember_me:
                        st.session_state.remember_me = True
                    st.success("✅ Login successful! Redirecting to dashboard...")
                    set_page("dashboard")
                    st.rerun()
                else:
                    st.error("❌ Invalid credentials. Please check your email and password.")

        # Additional login options
        st.markdown("---")

        col_forgot, col_register = st.columns(2)
        with col_forgot:
            if st.button("🔑 Forgot Password?", use_container_width=True):
                st.session_state['show_password_reset'] = True
                st.rerun()

        with col_register:
            if st.button("📝 New User?", use_container_width=True):
                st.session_state['show_user_registration'] = True
                st.rerun()

        # Statistics section
        st.markdown("---")
        st.markdown("### 📊 Community Stats")

        stats_col1, stats_col2 = st.columns(2)
        with stats_col1:
            st.metric("Active Alumni", "1,247", "↗️ 12%")
        with stats_col2:
            st.metric("Job Placements", "89", "↗️ 23%")

      

def show_login_page():
    # Use a smaller, centered container for the login form
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        <h2 style="text-align: center; margin-bottom: 1rem;">🔐 Login</h2>
        """, unsafe_allow_html=True)
        
        # Create a smaller card-like container for login
        st.markdown("""
        <div style="max-width: 400px; margin: 0 auto; padding: 1.5rem; 
                    border: 1px solid #ddd; border-radius: 8px; 
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        """, unsafe_allow_html=True)
        
        with st.form("simple_login_form"):
            email = st.text_input("Email", placeholder="Enter your email")
            password = st.text_input("Password", type="password", placeholder="Enter your password")
            
            # Center the login button
            col_a, col_b, col_c = st.columns([1, 2, 1])
            with col_b:
                login_btn = st.form_submit_button("Login")
            
            if login_btn:
                if authenticate_user(email, password):
                    st.session_state.authenticated = True
                    st.session_state.user_email = email
                    st.success("Login successful")
                    set_page("dashboard")
                    st.rerun()
                else:
                    st.error("Invalid credentials")
        
        st.markdown("</div>", unsafe_allow_html=True)
        
        # Add a smaller image below the login form
        try:
            col_img1, col_img2, col_img3 = st.columns([1, 3, 1])
            with col_img2:
                image = Image.open("images/alumni_banner.jpg")
                st.image(image, width=300)  # Set a fixed width
        except FileNotFoundError:
            pass  # Skip image if not found

def show_dashboard():
    user_email = st.session_state.get("user_email")
    role_list = get_user_permissions(user_email)
    role = role_list[0] if isinstance(role_list, list) and role_list else "unknown"

    # Create professional header if UI components available
    if UI_COMPONENTS_AVAILABLE:
        create_page_header(
            "🎓 Alumni Management System",
            f"Welcome back, {user_email}! You are logged in as {role.capitalize()}",
            "🎓"
        )

    # Enhanced sidebar with modern design
    with st.sidebar:
        # Logo and branding section
        try:
            st.image("images/logo.png", width=120)
        except FileNotFoundError:
            st.markdown("""
            <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, #667eea, #764ba2);
                        border-radius: 12px; margin-bottom: 1rem;">
                <h2 style="color: white; margin: 0; font-size: 1.5rem;">🎓</h2>
                <p style="color: rgba(255,255,255,0.9); margin: 0; font-size: 0.875rem;">DIT Alumni</p>
            </div>
            """, unsafe_allow_html=True)

        # User info card
        st.markdown(f"""
        <div class="custom-card" style="text-align: center; margin-bottom: 1rem;">
            <div style="background: linear-gradient(135deg, #667eea, #764ba2);
                        width: 60px; height: 60px; border-radius: 50%;
                        margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                <span style="color: white; font-size: 1.5rem; font-weight: bold;">
                    {user_email[0].upper() if user_email else 'U'}
                </span>
            </div>
            <h5 style="margin: 0 0 0.5rem 0; color: var(--text-primary);">{user_email or 'User'}</h5>
            <span class="status-badge active">{role.capitalize() if isinstance(role, str) else 'Unknown'}</span>
        </div>
        """, unsafe_allow_html=True)

        # Navigation menu
        st.markdown("### 🧭 Navigation")

        # Create subsystems list with better organization
        # Get unread message count for notifications
        unread_count = get_unread_message_count(user_email)
        chat_label = f"Feedback and Chat ({unread_count})" if unread_count > 0 else "Feedback and Chat"

        subsystems = ["Profile", "AI-powered Recommendations", "Messages", chat_label,
                     "Analytics and Reporting", "Events"]
        icons = ['person-circle', 'robot', 'envelope-fill', 'chat-dots-fill', 'graph-up', 'calendar-event']

        if role == "admin":
            subsystems.insert(1, "User Management")
            subsystems.insert(2, "Admin Tools")
            icons.insert(1, 'people-fill')
            icons.insert(2, 'gear-fill')

        selected = option_menu(
            None, subsystems,
            icons=icons,
            menu_icon=None,
            default_index=0,
            styles={
                "container": {"padding": "0", "background-color": "transparent"},
                "icon": {"color": "#64748b", "font-size": "1.1rem"},
                "nav-link": {
                    "font-size": "0.9rem",
                    "text-align": "left",
                    "margin": "0.25rem 0",
                    "padding": "0.75rem 1rem",
                    "border-radius": "8px",
                    "color": "#64748b",
                    "background-color": "transparent"
                },
                "nav-link-selected": {
                    "background": "linear-gradient(135deg, #2563eb, #1d4ed8)",
                    "color": "white",
                    "font-weight": "500"
                }
            }
        )

        # Logout button with default styling
        st.markdown("---")
        if st.button("🔓 Logout", key="logout_btn", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.user_email = None
            set_page("landing")
            st.rerun()

    # Main content area with welcome header
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, #f8fafc, #e2e8f0);
                padding: 2rem; border-radius: 16px; margin-bottom: 2rem;">
        <h1 style="color: #1e293b; margin: 0 0 0.5rem 0;">Welcome back! 👋</h1>
        <p style="color: #64748b; margin: 0; font-size: 1.1rem;">
            Ready to explore what's new in the DIT Alumni community?
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Handle the selected option
    if selected == "Profile":
        show_profile()
    elif selected == "User Management" and role == "admin":
        show_user_management(role)
    elif selected == "Admin Tools" and role == "admin":
        show_admin_tools(user_email)
    elif selected == "AI-powered Recommendations":
        show_ai_recommendations(user_email)
    elif selected == "Messages":
        show_messages_inbox(user_email)
    elif "Feedback and Chat" in selected:
        show_feedback_and_chat(user_email)
    elif selected == "Analytics and Reporting":
        show_analytics_and_reporting()
    elif selected == "Events":
        show_events_subsystem(role, user_email)

    # Profile viewing is now handled within individual components to avoid conflicts

def show_admin_dashboard():
    st.subheader("Admin Dashboard")
    st.markdown("Admins have access to all subsystems.")
    subsystem = st.sidebar.radio("Choose a subsystem", [
        "User Management",
        "AI-powered Recommendations",
        "Feedback and Chat",
        "Analytics and Reporting"
    ])
    if subsystem == "User Management":
        show_user_management("admin")
    elif subsystem == "AI-powered Recommendations":
        show_ai_recommendations(st.session_state.get("user_email"))
    elif subsystem == "Feedback and Chat":
        show_feedback_and_chat(st.session_state.get("user_email"))
    elif subsystem == "Analytics and Reporting":
        show_analytics_and_reporting()

def show_staff_dashboard():
    st.subheader("Staff Dashboard")
    st.markdown("Staff can manage student data and access analytics.")
    subsystem = st.sidebar.radio("Choose a subsystem", [
        "AI-powered Recommendations",
        "Feedback and Chat",
        "Analytics and Reporting"
    ])
    if subsystem == "AI-powered Recommendations":
        show_ai_recommendations(st.session_state.get("user_email"))
    elif subsystem == "Feedback and Chat":
        show_feedback_and_chat(st.session_state.get("user_email"))
    elif subsystem == "Analytics and Reporting":
        show_analytics_and_reporting()

def show_student_dashboard():
    st.subheader("Student Dashboard")
    st.markdown("Students can access personalized recommendations and chat with alumni.")
    subsystem = st.sidebar.radio("Choose a subsystem", [
        "AI-powered Recommendations",
        "Feedback and Chat"
    ])
    if subsystem == "AI-powered Recommendations":
        show_ai_recommendations(st.session_state.get("user_email"))
    elif subsystem == "Feedback and Chat":
        show_feedback_and_chat(st.session_state.get("user_email"))

def show_alumni_dashboard():
    st.subheader("Alumni Dashboard")
    st.markdown("Alumni can connect with peers and provide feedback.")
    subsystem = st.sidebar.radio("Choose a subsystem", [
        "Feedback and Chat",
        "Analytics and Reporting"
    ])
    if subsystem == "Feedback and Chat":
        show_feedback_and_chat(st.session_state.get("user_email"))
    elif subsystem == "Analytics and Reporting":
        show_analytics_and_reporting()

def show_user_management(role):
    """Enhanced user management with better presentation"""
    if role != "admin":
        st.error("Access denied. Admin privileges required.")
        return

    try:
        from enhanced_presentation import show_enhanced_user_management
        show_enhanced_user_management(role)
        return
    except ImportError:
        pass  # Fall back to original implementation

    # Enhanced header with statistics
    st.markdown("""
    <div class="custom-card">
        <h2 style="margin: 0 0 1rem 0;">👥 User Management</h2>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage users, roles, and permissions across the alumni portal
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats cards
    col1, col2, col3, col4 = st.columns(4)

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get user statistics
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'alumni'")
        alumni_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'student'")
        student_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        admin_count = cursor.fetchone()[0]

        with col1:
            st.metric("Total Users", total_users, "↗️ Active")
        with col2:
            st.metric("Alumni", alumni_count, f"{(alumni_count/total_users*100):.1f}%")
        with col3:
            st.metric("Students", student_count, f"{(student_count/total_users*100):.1f}%")
        with col4:
            st.metric("Admins", admin_count, f"{(admin_count/total_users*100):.1f}%")

        conn.close()
    except Exception as e:
        st.error(f"Error fetching statistics: {e}")

    # Enhanced tabs for managing users
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["👀 View All Users", "➕ Add User", "✏️ Edit User", "🗑️ Delete User", "⚙️ User Status"])

    # View All Users Tab
    with tab1:
        st.markdown("### 📋 User Directory")

        # Enhanced search and filter section
        st.markdown("""
        <div class="custom-card" style="margin-bottom: 1.5rem;">
            <h5 style="margin: 0 0 1rem 0;">🔍 Search & Filter</h5>
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3, col4 = st.columns([3, 2, 2, 1])
        with col1:
            search_query = st.text_input(
                "Search Users",
                placeholder="Search by name, email, or department...",
                help="Enter any part of the name, email, or department"
            )
        with col2:
            role_filter = st.selectbox(
                "Filter by Role",
                ["All Roles", "admin", "staff", "student", "alumni"],
                help="Filter users by their role in the system"
            )
        with col3:
            sort_by = st.selectbox(
                "Sort by",
                ["Name (A-Z)", "Name (Z-A)", "Email", "Role", "Recent"],
                help="Choose how to sort the user list"
            )
        with col4:
            items_per_page = st.selectbox("Per Page", [10, 25, 50, 100], index=1)
        
        # Fetch users from the database
        try:
            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                # Build the query based on search and filter
                query = "SELECT firstname, lastname, email, role, department, graduation_year FROM users"
                params = []
                
                # Add WHERE clause if needed
                where_clauses = []
                if search_query:
                    where_clauses.append("(firstname ILIKE %s OR lastname ILIKE %s OR email ILIKE %s)")
                    params.extend([f"%{search_query}%", f"%{search_query}%", f"%{search_query}%"])
                
                if role_filter != "All Roles":
                    where_clauses.append("role = %s")
                    params.append(role_filter)
                
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)
                
                # Add ORDER BY
                query += " ORDER BY lastname, firstname"
                
                cur.execute(query, params)
                users = cur.fetchall()
            conn.close()
            
            # Display users in a table with pagination
            if users:
                # Create a DataFrame for better display
                import pandas as pd
                users_df = pd.DataFrame(users)
                
                # Rename columns for better display
                users_df = users_df.rename(columns={
                    'firstname': 'First Name',
                    'lastname': 'Last Name',
                    'email': 'Email',
                    'role': 'Role',
                    'department': 'Department',
                    'graduation_year': 'Graduation Year'
                })
                
                # Calculate total pages and implement pagination
                total_users = len(users_df)
                total_pages = (total_users + items_per_page - 1) // items_per_page
                
                # Initialize page number in session state if not exists
                if 'user_page_number' not in st.session_state:
                    st.session_state.user_page_number = 0
                
                # Pagination controls
                col1, col2, col3 = st.columns([1, 3, 1])
                with col1:
                    if st.button("← Previous", disabled=st.session_state.user_page_number <= 0):
                        st.session_state.user_page_number -= 1
                        st.rerun()

                with col2:
                    st.markdown(f"<div style='text-align: center'>Page {st.session_state.user_page_number + 1} of {max(1, total_pages)}</div>", unsafe_allow_html=True)

                with col3:
                    if st.button("Next →", disabled=st.session_state.user_page_number >= total_pages - 1):
                        st.session_state.user_page_number += 1
                        st.rerun()
                
                # Get current page of data
                start_idx = st.session_state.user_page_number * items_per_page
                end_idx = min(start_idx + items_per_page, total_users)
                page_df = users_df.iloc[start_idx:end_idx]
                
                # Display the table with custom formatting
                st.dataframe(
                    page_df,
                    column_config={
                        "First Name": st.column_config.TextColumn(width="medium"),
                        "Last Name": st.column_config.TextColumn(width="medium"),
                        "Email": st.column_config.TextColumn(width="large"),
                        "Role": st.column_config.TextColumn(width="small"),
                        "Department": st.column_config.TextColumn(width="medium"),
                        "Graduation Year": st.column_config.NumberColumn(format="%d", width="small")
                    },
                    hide_index=True,
                    use_container_width=True
                )
                
                # Summary and export options
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown(f"**Showing {start_idx + 1}-{end_idx} of {total_users} users**")
                
                with col2:
                    if st.button("Export to CSV", key="export_users_btn"):
                        # Export all filtered users, not just current page
                        csv = users_df.to_csv(index=False)
                        st.download_button(
                            label="Download CSV",
                            data=csv,
                            file_name="users_export.csv",
                            mime="text/csv"
                        )
            else:
                st.info("No users found matching your criteria.")
        except Exception as e:
            st.error(f"Failed to fetch users: {e}")

    # Add User Tab
    with tab2:
        st.markdown("### Add a New User")
        with st.form("add_user_form"):
            firstname = st.text_input("First Name", placeholder="Enter first name")
            lastname = st.text_input("Last Name", placeholder="Enter last name")
            email = st.text_input("Email", placeholder="Enter user email")
            password = st.text_input("Password", type="password", placeholder="Enter user password")
            role = st.selectbox("Role", ["admin", "staff", "student", "alumni"])
            department = st.text_input("Department", placeholder="Enter department")
            interests = st.text_area("Interests", placeholder="Enter interests (comma-separated)")
            graduation_year = st.number_input("Graduation Year", min_value=1900, max_value=2100, step=1, value=2023) if role == "alumni" else None
            current_position = st.text_input("Current Position", placeholder="Enter current position (optional)")
            add_user_btn = st.form_submit_button("Add User")
            
            if add_user_btn:
                if email and password and firstname:
                    try:
                        # Call the register_user function to add the user to the database
                        register_user(
                            firstname, lastname, email, password, role, department,
                            interests.split(",") if interests else None, current_position, graduation_year
                        )
                        st.success(f"User {email} with role {role} added successfully!")
                        # Clear form by resetting session state
                        for key in st.session_state.keys():
                            if key.startswith('add_user_form-'):
                                del st.session_state[key]
                        time.sleep(0.8)  # Brief pause to show success message
                        st.rerun()  # Refresh the page to reset the form
                    except Exception as e:
                        st.error(f"Failed to add user: {e}")
                else:
                    st.error("Please provide all required fields.")

    # Edit User Tab
    with tab3:
        st.markdown("### Edit an Existing User")
        
        # Search functionality with autocomplete
        search_query = st.text_input("Search for user by email or name", placeholder="Enter email or name to search")
        
        if search_query:
            # Search for users matching the query
            try:
                matching_users = search_users(search_query)
                
                if matching_users:
                    # Create a formatted list for the selectbox
                    user_options = [f"{user['email']} ({user['firstname']} {user['lastname']})" for user in matching_users]
                    selected_user_option = st.selectbox("Select a user to edit:", user_options)
                    
                    # Extract email from the selected option
                    edit_user_email = selected_user_option.split(" (")[0]
                    
                    # Display a button to load the user details
                    load_user_btn = st.button("Load User Details")
                    
                    if load_user_btn:
                        user = get_user_by_email(edit_user_email)
                        if user:
                            st.success(f"User {edit_user_email} found. You can now edit their details.")
                            
                            # Store user in session state to maintain across reruns
                            st.session_state.edit_user = user
                            st.session_state.edit_user_email = edit_user_email
                            
                            # Rerun to show the form with user data
                            st.rerun()
                else:
                    st.warning(f"No users found matching '{search_query}'")
            except Exception as e:
                st.error(f"Error searching for users: {e}")
        
        # Check if we have a user to edit in session state
        if 'edit_user' in st.session_state and 'edit_user_email' in st.session_state:
            user = st.session_state.edit_user
            edit_user_email = st.session_state.edit_user_email
            
            # Create a form for editing user details
            with st.form("edit_user_form"):
                st.subheader(f"Editing: {edit_user_email}")
                
                # User details fields
                firstname = st.text_input("First Name", value=user["firstname"], key="edit_firstname")
                lastname = st.text_input("Last Name", value=user["lastname"], key="edit_lastname")
                role = st.selectbox("Role", ["admin", "staff", "student", "alumni"], 
                                   index=["admin", "staff", "student", "alumni"].index(user["role"]) if user["role"] in ["admin", "staff", "student", "alumni"] else 0, 
                                   key="edit_role")
                department = st.text_input("Department", value=user["department"] or "", key="edit_department")
                
                # Handle interests (convert list to string for editing)
                interests_str = ", ".join(user["interests"]) if user["interests"] else ""
                interests = st.text_area("Interests (comma-separated)", value=interests_str, key="edit_interests")
                
                # Conditional fields based on role
                graduation_year = None
                if role == "alumni":
                    graduation_year = st.number_input("Graduation Year", min_value=1900, max_value=2100, step=1, 
                                                     value=user["graduation_year"] or 2023, key="edit_graduation_year")
                
                current_position = st.text_input("Current Position", value=user["current_position"] or "", key="edit_current_position")
                
                # Submit button
                update_btn = st.form_submit_button("Update User")
                
                if update_btn:
                    try:
                        # Prepare update data
                        update_data = {
                            "firstname": firstname,
                            "lastname": lastname,
                            "role": role,
                            "department": department,
                            "interests": interests.split(",") if interests else None,
                            "current_position": current_position
                        }
                        
                        # Add graduation_year only if role is alumni
                        if role == "alumni":
                            update_data["graduation_year"] = graduation_year
                        
                        # Update user in database
                        update_user_details(edit_user_email, **update_data)
                        st.success(f"User {edit_user_email} updated successfully!")
                        
                        # Clear session state
                        del st.session_state.edit_user
                        del st.session_state.edit_user_email
                        
                        # Rerun to reset the form
                        st.rerun()
                    except Exception as e:
                        st.error(f"Failed to update user: {e}")
            
            # Add a cancel button outside the form
            if st.button("Cancel Editing"):
                # Clear session state
                del st.session_state.edit_user
                del st.session_state.edit_user_email
                st.rerun()

    # Delete User Tab
    with tab4:
        st.markdown("### Delete a User")
        
        # Enhanced search functionality
        search_query = st.text_input("Search for user to delete", placeholder="Enter email or name", key="delete_search")
        
        if search_query:
            try:
                # Search for users matching the query
                matching_users = search_users(search_query)
                
                if matching_users:
                    # Create a formatted list for the selectbox with email and full name
                    user_options = [f"{user['email']} ({user['firstname']} {user['lastname']})" for user in matching_users]
                    selected_user_option = st.selectbox("Select a user to delete:", user_options, key="delete_user_select")
                    
                    # Extract email from the selected option
                    delete_user_email = selected_user_option.split(" (")[0]
                    
                    # Create a container for the user details
                    user_details_container = st.container()
                    
                    with user_details_container:
                        # Fetch complete user details
                        user = get_user_by_email(delete_user_email)
                        
                        if user:
                            # Display user details in a card-like format
                            st.markdown("""
                            <style>
                            .user-card {
                                border: 1px solid #f0f0f0;
                                border-radius: 10px;
                                padding: 15px;
                                margin-bottom: 15px;
                                background-color: #f9f9f9;
                            }
                            .user-card h4 {
                                margin-top: 0;
                                color: #ff5555;
                            }
                            .user-field {
                                margin-bottom: 8px;
                            }
                            .user-field strong {
                                display: inline-block;
                                width: 140px;
                            }
                            </style>
                            """, unsafe_allow_html=True)
                            
                            st.markdown(f"""
                            <div class="user-card">
                                <h4>User Details</h4>
                                <div class="user-field"><strong>Email:</strong> {user['email']}</div>
                                <div class="user-field"><strong>Name:</strong> {user['firstname']} {user['lastname']}</div>
                                <div class="user-field"><strong>Role:</strong> {user['role']}</div>
                                <div class="user-field"><strong>Department:</strong> {user['department'] or 'Not specified'}</div>
                                <div class="user-field"><strong>Interests:</strong> {', '.join(user['interests']) if user['interests'] else 'None'}</div>
                                <div class="user-field"><strong>Current Position:</strong> {user['current_position'] or 'Not specified'}</div>
                                <div class="user-field"><strong>Graduation Year:</strong> {user['graduation_year'] if user['graduation_year'] else 'N/A'}</div>
                            </div>
                            """, unsafe_allow_html=True)
                            
                            # Add a warning message
                            st.warning("⚠️ **Warning:** Deleting a user is permanent and cannot be undone. All associated data will be lost.")
                            
                            # Two-step deletion process with confirmation
                            col1, col2 = st.columns([1, 3])
                            with col1:
                                st.markdown("""
                                <style>
                                button[key="delete_user_btn"] {
                                    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
                                    color: white !important;
                                    font-weight: 600 !important;
                                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
                                    border: none !important;
                                }
                                button[key="delete_user_btn"]:hover {
                                    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
                                    color: white !important;
                                }
                                </style>
                                """, unsafe_allow_html=True)
                                delete_btn = st.button("Delete User", key="delete_user_btn", type="primary")
                            
                            if delete_btn:
                                # Show confirmation dialog
                                st.session_state.show_delete_confirm = True
                            
                            # Handle confirmation dialog
                            if st.session_state.get("show_delete_confirm", False):
                                st.markdown("### Confirm Deletion")
                                st.error(f"Are you sure you want to permanently delete user **{delete_user_email}**?")
                                
                                col1, col2 = st.columns([1, 1])
                                with col1:
                                    if st.button("Yes, Delete User", key="confirm_delete_btn"):
                                        try:
                                            # Delete the user
                                            delete_user_by_email(delete_user_email)
                                            st.success(f"User {delete_user_email} has been deleted successfully.")
                                            
                                            # Reset session state
                                            st.session_state.show_delete_confirm = False
                                            
                                            # Clear the search field and selection
                                            st.session_state.delete_search = ""
                                            
                                            # Rerun to refresh the page
                                            time.sleep(1)  # Brief pause for the success message to be seen
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"Failed to delete user: {e}")
                                with col2:
                                    if st.button("Cancel", key="cancel_delete_btn"):
                                        st.session_state.show_delete_confirm = False
                                        st.rerun()
                        else:
                            st.error(f"Failed to retrieve details for user {delete_user_email}.")
                else:
                    st.warning(f"No users found matching '{search_query}'")
            except Exception as e:
                st.error(f"Error searching for users: {e}")

    # User Status Management Tab
    with tab5:
        st.markdown("### User Status Management")
        st.markdown("Manage user activation, deactivation, and confirmation status")

        # Search for user
        search_query = st.text_input("Search for user to manage status", placeholder="Enter email or name", key="status_search")

        if search_query:
            try:
                matching_users = search_users(search_query)

                if matching_users:
                    user_options = [f"{user['email']} ({user['firstname']} {user['lastname']})" for user in matching_users]
                    selected_user_option = st.selectbox("Select a user to manage:", user_options, key="status_user_select")

                    # Extract email from the selected option
                    manage_user_email = selected_user_option.split(" (")[0]

                    # Get current user status
                    user = get_user_by_email(manage_user_email)
                    if user:
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("#### Current Status")
                            is_active = user.get('is_active', True)
                            is_confirmed = user.get('is_confirmed', False)
                            confirmed_by = user.get('confirmed_by', 'Not confirmed')
                            confirmed_at = user.get('confirmed_at', 'Never')

                            st.info(f"""
                            **User:** {user['firstname']} {user['lastname']}
                            **Email:** {user['email']}
                            **Role:** {user['role']}
                            **Active:** {'✅ Yes' if is_active else '❌ No'}
                            **Confirmed:** {'✅ Yes' if is_confirmed else '❌ No'}
                            **Confirmed By:** {confirmed_by}
                            **Confirmed At:** {confirmed_at}
                            """)

                        with col2:
                            st.markdown("#### Actions")

                            # Activation/Deactivation
                            if is_active:
                                if st.button("🔒 Deactivate User", key="deactivate_btn"):
                                    update_user_status(manage_user_email, is_active=False)
                                    st.success(f"User {manage_user_email} has been deactivated.")
                                    st.rerun()
                            else:
                                if st.button("🔓 Activate User", key="activate_btn"):
                                    update_user_status(manage_user_email, is_active=True)
                                    st.success(f"User {manage_user_email} has been activated.")
                                    st.rerun()

                            # Confirmation
                            if not is_confirmed:
                                if st.button("✅ Confirm User", key="confirm_btn"):
                                    admin_email = st.session_state.get("user_email")
                                    confirm_user(manage_user_email, admin_email)
                                    st.success(f"User {manage_user_email} has been confirmed.")
                                    st.rerun()
                            else:
                                if st.button("❌ Revoke Confirmation", key="revoke_confirm_btn"):
                                    revoke_user_confirmation(manage_user_email)
                                    st.success(f"Confirmation revoked for {manage_user_email}.")
                                    st.rerun()
                else:
                    st.warning(f"No users found matching '{search_query}'")
            except Exception as e:
                st.error(f"Error searching for users: {e}")

def evaluate_and_select_best_model():
    """
    Evaluate three ML models, display their performance, and select the best one based on accuracy.
    """
    try:
        # Load training data from train_recommendation.csv in the data folder
        data_path = os.path.join("data", "train_recommendation.csv")
        if not os.path.exists(data_path):
            st.error("Training data not found. Please ensure 'train_recommendation.csv' exists in the 'data' folder.")
            return None

        df = pd.read_csv(data_path)
        X = df["Recommendation"]
        y = df["Type"]

        # Convert text data to feature vectors
        vectorizer = CountVectorizer()
        X_vectorized = vectorizer.fit_transform(X)

        # Split data into training and testing sets
        X_train, X_test, y_train, y_test = train_test_split(X_vectorized, y, test_size=0.2, random_state=42)

        # Train models
        models = {
            "Naive Bayes": MultinomialNB(),
            "Decision Tree": DecisionTreeClassifier(),
            "Random Forest": RandomForestClassifier()
        }
        accuracies = {}
        confidence_scores = {}

        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracies[name] = accuracy_score(y_test, y_pred)
            confidence_scores[name] = model.score(X_test, y_test)

        # Display evaluation results
        st.subheader("Model Evaluation Results")
        st.bar_chart(accuracies)
        st.markdown("### Confidence Scores")
        for model_name, confidence in confidence_scores.items():
            st.write(f"- **{model_name}**: {confidence:.2f}")

        # Select the best model
        best_model_name = max(accuracies, key=accuracies.get)
        best_model = models[best_model_name]

        # Save the best model and vectorizer
        model_path = os.path.join("models", "best_recommendation_model.pkl")
        vectorizer_path = os.path.join("models", "vectorizer.pkl")
        os.makedirs("models", exist_ok=True)
        joblib.dump(best_model, model_path)
        joblib.dump(vectorizer, vectorizer_path)

        st.success(f"Best model '{best_model_name}' trained and saved with accuracy: {accuracies[best_model_name]:.2f}")
        return best_model, vectorizer
    except Exception as e:
        st.error(f"Failed to evaluate models: {e}")
        return None

def show_ai_recommendations(user_email):
    st.subheader("🤖 AI-Powered Recommendations & Decision Support")

    # Import enhanced AI engine
    try:
        from enhanced_ai_engine import get_enhanced_recommendations, train_enhanced_models, recommendation_engine
    except ImportError:
        st.error("Enhanced AI engine not found. Please ensure enhanced_ai_engine.py is available.")
        return

    # Create tabs for different recommendation types
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "🎯 Personalized", "👥 Who to Contact", "📚 What to Read",
        "🎓 Learning Path", "⚙️ AI Settings"
    ])

    with tab1:
        st.markdown("### 🎯 Your Personalized AI Recommendations")

        # Add refresh button
        col1, col2 = st.columns([3, 1])
        with col2:
            if st.button("🔄 Refresh Recommendations"):
                st.rerun()

        with st.spinner("🧠 AI is analyzing your profile and generating recommendations..."):
            recommendations = get_enhanced_recommendations(user_email)

        # Display personalized message
        if recommendations.get('personalized_message'):
            st.success(recommendations['personalized_message'])

        # Create metrics for recommendation overview
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Content-Based", len(recommendations.get('content_based', [])))
        with col2:
            st.metric("Collaborative", len(recommendations.get('collaborative', [])))
        with col3:
            st.metric("Networking", len(recommendations.get('networking', [])))
        with col4:
            st.metric("Reading", len(recommendations.get('reading', [])))

        # Content-based recommendations
        if recommendations.get('content_based'):
            st.markdown("#### 🎯 AI-Powered Recommendations Based on Your Interests")
            for i, rec in enumerate(recommendations['content_based'], 1):
                # Create a more attractive card-like display
                with st.container():
                    col1, col2 = st.columns([4, 1])
                    with col1:
                        st.markdown(f"**{rec['type'].title()} Recommendation {i}**")
                        st.write(rec['text'])
                        st.caption(f"Category: {rec['category'].title()} | Priority: {'⭐' * rec['priority']}")
                    with col2:
                        if rec['type'] == 'event':
                            st.button("📅 Interested", key=f"event_{i}")
                        elif rec['type'] == 'course':
                            st.button("📚 Learn More", key=f"course_{i}")
                        elif rec['type'] == 'contact':
                            st.button("👋 Connect", key=f"contact_{i}")
                    st.divider()

        # Collaborative recommendations
        if recommendations.get('collaborative'):
            st.markdown("#### 👥 Recommendations Based on Similar Alumni")
            for i, rec in enumerate(recommendations['collaborative'], 1):
                st.info(f"🔗 {rec['text']}")

        # Show recommendation accuracy info
        with st.expander("ℹ️ How AI Recommendations Work"):
            st.write("""
            Our AI recommendation system uses multiple advanced techniques:

            🎯 **Content-Based Filtering**: Analyzes your interests and matches them with relevant opportunities

            👥 **Collaborative Filtering**: Finds alumni with similar profiles and suggests what they found valuable

            🧠 **Machine Learning Models**: Uses TF-IDF vectorization and ensemble methods for accurate predictions

            📊 **Continuous Learning**: The system improves as more alumni interact with recommendations
            """)

    with tab2:
        st.markdown("### 👥 Smart Networking Recommendations")

        networking_recs = recommendations.get('networking', [])
        if networking_recs:
            st.markdown("#### 🎯 AI-Recommended Connections")
            for i, rec in enumerate(networking_recs, 1):
                with st.container():
                    col1, col2, col3 = st.columns([3, 1, 1])
                    with col1:
                        st.markdown(f"**Connection {i}**")
                        st.write(f"💼 {rec['text']}")
                        st.caption(f"Match Score: {'⭐' * rec['priority']}")
                    with col2:
                        if 'email' in rec:
                            if st.button("💬 Start Chat", key=f"chat_{rec['email']}"):
                                st.session_state['chat_with'] = rec['email']
                                st.success(f"Ready to chat with {rec['email']}")
                    with col3:
                        if st.button("👤 View Profile", key=f"profile_{i}"):
                            if 'email' in rec:
                                st.session_state[f'view_profile_{rec["email"]}'] = True
                                st.rerun()
                            else:
                                st.warning("Profile information not available for this recommendation")
                    st.divider()
        else:
            st.info("🔍 No specific networking recommendations found. Try updating your interests!")

        # Add smart networking suggestions based on user data
        st.markdown("#### 🌟 Smart Networking Opportunities")

        # Get user's department and role for targeted suggestions
        user = get_user_by_email(user_email)
        if user:
            dept = user.get('department', 'Unknown')
            role = user.get('role', 'Unknown')

            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**🎓 Department-Based Connections**")
                st.write(f"• Connect with other {dept} alumni")
                st.write(f"• Join {dept} professional groups")
                st.write(f"• Attend {dept} department events")

            with col2:
                st.markdown("**💼 Role-Based Networking**")
                if role == 'student':
                    st.write("• Connect with alumni in your target industry")
                    st.write("• Find mentors in senior positions")
                    st.write("• Join student-alumni mentorship programs")
                elif role == 'alumni':
                    st.write("• Mentor current students")
                    st.write("• Connect with industry peers")
                    st.write("• Join alumni leadership groups")
                else:
                    st.write("• Connect with industry professionals")
                    st.write("• Join professional associations")
                    st.write("• Attend networking events")

        # Add networking action items
        st.markdown("#### 📋 Networking Action Plan")
        with st.expander("📝 Your Personalized Networking Strategy"):
            st.write("""
            **This Week:**
            • Send 2-3 connection requests to recommended alumni
            • Update your profile with current interests and goals
            • Join one relevant alumni group or forum

            **This Month:**
            • Attend at least one virtual networking event
            • Schedule coffee chats with 2-3 new connections
            • Share valuable content in alumni groups

            **This Quarter:**
            • Establish 5-10 meaningful professional relationships
            • Offer help or mentorship to others
            • Attend in-person alumni events when possible
            """)

    with tab3:
        st.markdown("### 📚 Personalized Reading & Learning Recommendations")

        reading_recs = recommendations.get('reading', [])
        if reading_recs:
            st.markdown("#### 📖 AI-Curated Reading List")
            for i, rec in enumerate(reading_recs, 1):
                with st.container():
                    col1, col2 = st.columns([4, 1])
                    with col1:
                        # Extract book/resource title and description
                        title = rec['text'].split(' - ')[0] if ' - ' in rec['text'] else rec['text']
                        description = rec['text'].split(' - ')[1] if ' - ' in rec['text'] else "Recommended resource"

                        st.markdown(f"**📚 {title}**")
                        st.write(description)
                        st.caption(f"Priority: {'⭐' * rec['priority']} | Category: {rec['category']}")
                    with col2:
                        if st.button("🔖 Save", key=f"save_reading_{i}"):
                            st.success("Added to reading list!")
                    st.divider()
        else:
            st.info("🔍 No specific reading recommendations found. General resources below!")

        # Add categorized learning resources based on user interests
        user = get_user_by_email(user_email)
        if user and user.get('interests'):
            interests = user['interests']
            if isinstance(interests, list):
                interests_str = ', '.join(interests).lower()
            else:
                interests_str = str(interests).lower()

            st.markdown("#### 🎯 Resources Tailored to Your Interests")

            # Technology resources
            if any(tech in interests_str for tech in ['ai', 'ml', 'software', 'programming', 'data']):
                with st.expander("💻 Technology & Programming Resources"):
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown("**📚 Essential Books:**")
                        st.write("• Clean Code by Robert Martin")
                        st.write("• Deep Learning by Ian Goodfellow")
                        st.write("• Python for Data Analysis by Wes McKinney")
                        st.write("• The Pragmatic Programmer")
                    with col2:
                        st.markdown("**🌐 Online Resources:**")
                        st.write("• [Coursera ML Course](https://coursera.org)")
                        st.write("• [Kaggle Learn](https://kaggle.com/learn)")
                        st.write("• [freeCodeCamp](https://freecodecamp.org)")
                        st.write("• [MIT OpenCourseWare](https://ocw.mit.edu)")

            # Business resources
            if any(biz in interests_str for biz in ['business', 'entrepreneur', 'marketing', 'finance']):
                with st.expander("💼 Business & Entrepreneurship Resources"):
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown("**📚 Business Books:**")
                        st.write("• The Lean Startup by Eric Ries")
                        st.write("• Good to Great by Jim Collins")
                        st.write("• The Intelligent Investor by Benjamin Graham")
                        st.write("• Zero to One by Peter Thiel")
                    with col2:
                        st.markdown("**🌐 Business Resources:**")
                        st.write("• [Harvard Business Review](https://hbr.org)")
                        st.write("• [McKinsey Insights](https://mckinsey.com)")
                        st.write("• [Y Combinator Startup School](https://startupschool.org)")
                        st.write("• [Google Digital Marketing](https://learndigital.withgoogle.com)")

        # General learning resources
        st.markdown("#### 🎓 General Learning Platforms")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("**🎯 Online Courses**")
            st.write("• Coursera")
            st.write("• edX")
            st.write("• Udacity")
            st.write("• Khan Academy")

        with col2:
            st.markdown("**💼 Professional Development**")
            st.write("• LinkedIn Learning")
            st.write("• Pluralsight")
            st.write("• MasterClass")
            st.write("• Skillshare")

        with col3:
            st.markdown("**📰 Industry Insights**")
            st.write("• Medium")
            st.write("• Dev.to")
            st.write("• Towards Data Science")
            st.write("• Industry podcasts")

        # Reading tracker
        st.markdown("#### 📊 Your Learning Progress")
        with st.expander("📈 Track Your Learning Journey"):
            st.write("**Reading Goals for This Month:**")
            col1, col2 = st.columns(2)
            with col1:
                books_goal = st.number_input("Books to read:", min_value=0, max_value=10, value=2)
                articles_goal = st.number_input("Articles to read:", min_value=0, max_value=50, value=10)
            with col2:
                courses_goal = st.number_input("Courses to complete:", min_value=0, max_value=5, value=1)
                if st.button("💾 Save Goals"):
                    st.success("Learning goals saved!")

    with tab4:
        st.markdown("### 🎓 Personalized Learning Path")

        # Get user info for learning path
        user = get_user_by_email(user_email)
        if user and user.get('interests'):
            interests = user['interests']
            if isinstance(interests, list):
                interests_str = ', '.join(interests)
            else:
                interests_str = str(interests)

            st.markdown(f"#### Based on your interests: {interests_str}")

            # Create learning path based on interests
            learning_path = create_learning_path(interests)

            for i, step in enumerate(learning_path, 1):
                with st.expander(f"Step {i}: {step['title']}"):
                    st.write(f"**Duration:** {step['duration']}")
                    st.write(f"**Description:** {step['description']}")
                    st.write(f"**Resources:** {step['resources']}")
        else:
            st.info("Please update your interests to get a personalized learning path.")

    with tab5:
        st.markdown("### ⚙️ AI Model Settings & Training")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### Model Status")
            model_path = os.path.join("models", "content_based_model.pkl")
            if os.path.exists(model_path):
                st.success("✅ Enhanced AI model is ready")
            else:
                st.warning("⚠️ Enhanced AI model not found")

            if st.button("🔄 Train Enhanced AI Models"):
                with st.spinner("Training enhanced AI models... This may take a few minutes."):
                    success = train_enhanced_models()
                if success:
                    st.success("Enhanced AI models trained successfully!")
                    st.rerun()
                else:
                    st.error("Failed to train models. Check the logs.")

        with col2:
            st.markdown("#### Model Performance")
            if os.path.exists(model_path):
                try:
                    # Show some basic stats about the model
                    st.write("📊 **Model Statistics:**")
                    st.write("• Algorithm: Random Forest / Naive Bayes / Logistic Regression")
                    st.write("• Features: TF-IDF Vectorization")
                    st.write("• Training Data: 70+ recommendation scenarios")
                    st.write("• Categories: 4 (course, contact, reading, event)")
                except Exception as e:
                    st.write("Model information not available")

            if st.button("📈 Evaluate Model Performance"):
                with st.spinner("Evaluating model performance..."):
                    # This would show detailed performance metrics
                    st.info("Model evaluation completed. Check logs for details.")

    # Button to generate the personalized recommendation CSV
    if st.button("Generate Personalized Recommendation CSV"):
        with st.spinner("Generating CSV..."):
            result = generate_recommendation_csv(user_email)
            if result:
                df, file_name = result
                
                # Display the CSV content in a table
                st.subheader("Generated Recommendations")
                st.dataframe(df)
                
                # Provide a download button
                csv = df.to_csv(index=False)
                st.download_button(
                    label="Download Recommendations CSV",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

def show_feedback_and_chat(user_email):
    # Enhanced header
    st.markdown("""
    <div class="custom-card">
        <h2 style="margin: 0 0 1rem 0;">💬 Feedback & Communication</h2>
        <p style="color: var(--text-secondary); margin: 0;">
            Share your thoughts and connect with the alumni community
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Create enhanced tabs
    tab1, tab2 = st.tabs(["📝 Submit Feedback", "💬 Community Chat"])

    # Tab 1: Enhanced Feedback System
    with tab1:
        col1, col2 = st.columns([2, 1], gap="large")

        # Left column - Feedback form
        with col1:
            st.markdown("""
            <div class="custom-card">
                <h4 style="margin: 0 0 1rem 0;">📋 Share Your Feedback</h4>
                <p style="color: var(--text-secondary); margin: 0;">
                    Your feedback helps us improve the alumni portal experience for everyone.
                </p>
            </div>
            """, unsafe_allow_html=True)

            with st.form("feedback_form"):
                # Enhanced feedback type selection
                feedback_type = st.selectbox(
                    "What type of feedback are you sharing?",
                    ["💡 General Feedback", "🚀 Feature Request", "🐛 Bug Report", "📚 Content Suggestion", "⭐ Testimonial"],
                    help="Select the category that best describes your feedback"
                )

                # Enhanced feedback text area
                feedback_text = st.text_area(
                    "Your Feedback",
                    placeholder="Please share your detailed thoughts, suggestions, or report issues here...",
                    height=150,
                    help="Be as specific as possible to help us understand and address your feedback"
                )

                # Rating with better styling
                st.markdown("##### Rate Your Experience")
                rating = st.slider(
                    "Overall satisfaction (1-5 stars)",
                    1, 5, 3,
                    help="1 = Very Dissatisfied, 5 = Very Satisfied"
                )

                # Display rating visually
                stars = "⭐" * rating + "☆" * (5 - rating)
                st.markdown(f"**Your Rating:** {stars} ({rating}/5)")

                # Additional options
                col_anon, col_follow = st.columns(2)
                with col_anon:
                    anonymous = st.checkbox("Submit anonymously", help="Your name won't be associated with this feedback")
                with col_follow:
                    follow_up = st.checkbox("I'd like a follow-up response", value=True)

                # Enhanced submit button
                submit_feedback = st.form_submit_button("🚀 Submit Feedback", use_container_width=True)
            
            if submit_feedback:
                if feedback_text.strip():
                    try:
                        # Get user ID
                        user_id = get_user_id_by_email(user_email)
                        
                        # Insert feedback into database
                        conn = get_db_connection()
                        with conn.cursor() as cur:
                            cur.execute("""
                                INSERT INTO feedback (user_id, feedback_type, feedback_text, rating, submitted_at)
                                VALUES (%s, %s, %s, %s, NOW())
                            """, (user_id, feedback_type, feedback_text, rating))
                        conn.commit()
                        conn.close()
                        
                        st.success("Thank you for your feedback!")
                        # Clear form by resetting session state
                        for key in st.session_state.keys():
                            if key.startswith('feedback_form-'):
                                del st.session_state[key]
                        time.sleep(0.8)  # Brief pause to show success message
                        st.rerun()  # Refresh the page to reset the form
                    except Exception as e:
                        st.error(f"Failed to submit feedback: {e}")
                else:
                    st.error("Please enter your feedback before submitting.")
        
        # Display previous feedback from this user
        st.markdown("### Your Previous Feedback")
        try:
            user_id = get_user_id_by_email(user_email)
            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT feedback_type, feedback_text, rating, submitted_at
                    FROM feedback
                    WHERE user_id = %s
                    ORDER BY submitted_at DESC
                """, (user_id,))
                previous_feedback = cur.fetchall()
            conn.close()
            
            if previous_feedback:
                for feedback in previous_feedback:
                    submitted_at = feedback["submitted_at"].strftime("%Y-%m-%d %H:%M:%S")
                    st.markdown(f"""
                    <div style="border: 1px solid #ddd; border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                        <p><strong>Type:</strong> {feedback["feedback_type"]}</p>
                        <p><strong>Rating:</strong> {"⭐" * feedback["rating"]}</p>
                        <p><strong>Submitted:</strong> {submitted_at}</p>
                        <p>{feedback["feedback_text"]}</p>
                    </div>
                    """, unsafe_allow_html=True)
            else:
                st.info("You haven't submitted any feedback yet.")
        except Exception as e:
            st.error(f"Failed to load previous feedback: {e}")
    
    # Tab 2: Chat System
    with tab2:
        st.markdown("### Chat with Members")
        
        # Checkbox for "All Members"
        all_members = st.checkbox("Chat with All Members")

        # Searchable text input for specific user
        receiver_email = None
        if not all_members:
            search_query = st.text_input("Search for a member by email or name", placeholder="Enter email or name")
            if search_query.strip():
                matching_users = search_users(search_query)  # Function to search users by email or name
                if matching_users:
                    receiver_email = st.selectbox(
                        "Select a member to chat with",
                        [user["email"] for user in matching_users],
                        format_func=lambda email: f"{email} ({get_user_name_by_email(email)})"  # Display email with name
                    )
                else:
                    st.warning("No matching users found.")

        # Display chat history
        st.markdown("### Chat History")
        if not all_members and not receiver_email:
            st.info("No chat history to display. Please select a member or choose 'Chat with All Members'.")
        else:
            chat_history = get_chat_history(user_email, "All Members" if all_members else receiver_email)
            if not chat_history:
                st.info("No chat history available.")
            else:
                # Mark messages as read when viewing
                if receiver_email and receiver_email != "All Members":
                    mark_messages_as_read(user_email, receiver_email)

                for chat in chat_history:
                    sent_at = chat["sent_at"].strftime("%Y-%m-%d %H:%M:%S")  # Format the sent date and time
                    sender_name = get_user_name_by_email_from_id(chat['sender'])

                    # Check if message is unread (for current user as receiver)
                    current_user_id = get_user_id_by_email(user_email)
                    is_unread = (chat.get('receiver_id') == current_user_id and
                               chat.get('is_read', True) == False)

                    col1, col2 = st.columns([8, 1])  # Create two columns for message and delete button
                    with col1:
                        # Add notification indicator for unread messages
                        unread_indicator = "🔴 " if is_unread else ""
                        st.markdown(f"{unread_indicator}**{sender_name}** ({sent_at}): {chat['message']}")
                    with col2:
                        if st.button("❌", key=f"delete_{chat['id']}"):  # Individual chat delete button
                            delete_chat(chat["id"])
                            st.success("Chat deleted successfully!")
                            st.rerun()  # Refresh the page after deletion

            # Input for sending a new message
            st.markdown("### Send a Message")
            chat_message = st.text_area("Enter your message")
            if st.button("Send"):
                if chat_message.strip():
                    try:
                        send_chat_message(user_email, receiver_email, chat_message)
                        st.success(f"Message sent to {receiver_email}!")
                        time.sleep(0.5)  # Brief pause to show success message
                        st.rerun()  # Refresh the page after sending the message
                    except Exception as e:
                        st.error(f"Failed to send message: {e}")
                else:
                    st.error("Message cannot be empty.")

def delete_chat_history(user_email, receiver_email):
    """
    Delete the entire chat history between the user and the selected receiver.
    """
    try:
        sender_id = get_user_id_by_email(user_email)
        receiver_id = get_user_id_by_email(receiver_email) if receiver_email != "All Members" else "All Members"

        if not sender_id:
            st.error("Failed to fetch sender ID.")
            return

        conn = get_db_connection()
        with conn.cursor() as cur:
            if receiver_id == "All Members":
                cur.execute("""
                    DELETE FROM chats
                    WHERE receiver_id = %s OR sender_id = %s
                """, ("All Members", sender_id))
            else:
                cur.execute("""
                    DELETE FROM chats
                    WHERE (sender_id = %s AND receiver_id = %s) OR (sender_id = %s AND receiver_id = %s)
                """, (sender_id, receiver_id, receiver_id, sender_id))
        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to delete chat history: {e}")

# Database functions for chat system
def get_all_user_emails():
    """
    Fetch all user emails from the database for the chat dropdown.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT email FROM users")
            users = cur.fetchall()
        conn.close()
        return [user["email"] for user in users]
    except Exception as e:
        st.error(f"Failed to fetch user emails: {e}")
        return []

def get_user_id_by_email(email):
    """
    Fetch the user ID corresponding to the given email.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT id FROM users WHERE email = %s", (email,))
            user = cur.fetchone()
        conn.close()
        return user["id"] if user else None
    except Exception as e:
        st.error(f"Failed to fetch user ID: {e}")
        return None

def get_chat_history(user_email, receiver_email):
    """
    Fetch chat history between the user and the selected receiver.
    """
    try:
        sender_id = get_user_id_by_email(user_email)

        if not sender_id:
            st.error("Failed to fetch sender ID.")
            return []

        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            if receiver_email == "All Members":
                # Fetch messages sent by the user to all members
                cur.execute("""
                    SELECT id, sender_id AS sender, receiver_id, message, sent_at, is_read FROM chats
                    WHERE sender_id = %s
                    ORDER BY sent_at ASC
                """, (sender_id,))
            else:
                receiver_id = get_user_id_by_email(receiver_email)
                if not receiver_id:
                    st.error("Failed to fetch receiver ID.")
                    return []

                # Fetch messages between the user and the specific receiver
                cur.execute("""
                    SELECT id, sender_id AS sender, receiver_id, message, sent_at, is_read FROM chats
                    WHERE (sender_id = %s AND receiver_id = %s) OR (sender_id = %s AND receiver_id = %s)
                    ORDER BY sent_at ASC
                """, (sender_id, receiver_id, receiver_id, sender_id))

            chats = cur.fetchall()
        conn.close()
        return chats
    except Exception as e:
        st.error(f"Failed to fetch chat history: {e}")
        return []

def send_chat_message(sender_email, receiver_email, message):
    """
    Send a chat message to the database.
    """
    try:
        sender_id = get_user_id_by_email(sender_email)  # Fetch sender ID

        if not sender_id:
            st.error("Failed to fetch sender ID.")
            return

        conn = get_db_connection()
        with conn.cursor() as cur:
            if receiver_email == "All Members":
                # Fetch all user IDs except the sender
                cur.execute("SELECT id FROM users WHERE id != %s", (sender_id,))
                all_user_ids = [user["id"] for user in cur.fetchall()]

                # Insert a message for each user
                for receiver_id in all_user_ids:
                    cur.execute("""
                        INSERT INTO chats (sender_id, receiver_id, message, sent_at, is_read)
                        VALUES (%s, %s, %s, NOW(), FALSE)
                    """, (sender_id, receiver_id, message))
            else:
                receiver_id = get_user_id_by_email(receiver_email)
                if not receiver_id:
                    st.error("Failed to fetch receiver ID.")
                    return

                # Insert a single message for the specific receiver
                cur.execute("""
                    INSERT INTO chats (sender_id, receiver_id, message, sent_at, is_read)
                    VALUES (%s, %s, %s, NOW(), FALSE)
                """, (sender_id, receiver_id, message))

        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to send message: {e}")

def delete_chat(chat_id):
    """
    Delete a chat message by its ID.
    """
    try:
        conn = get_db_connection()
        with conn.cursor() as cur:
            cur.execute("DELETE FROM chats WHERE id = %s", (chat_id,))
        conn.commit()
        conn.close()
    except Exception as e:
        st.error(f"Failed to delete chat: {e}")

def get_unread_message_count(user_email):
    """
    Get the count of unread messages for a user.
    """
    try:
        user_id = get_user_id_by_email(user_email)
        if not user_id:
            return 0

        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT COUNT(*) as unread_count
                FROM chats
                WHERE receiver_id = %s AND is_read = FALSE
            """, (user_id,))
            result = cur.fetchone()
        conn.close()
        return result['unread_count'] if result else 0
    except Exception as e:
        # If is_read column doesn't exist, return 0
        return 0

def mark_messages_as_read(user_email, sender_email=None):
    """
    Mark messages as read for a user.
    """
    try:
        user_id = get_user_id_by_email(user_email)
        if not user_id:
            return

        conn = get_db_connection()
        with conn.cursor() as cur:
            if sender_email:
                sender_id = get_user_id_by_email(sender_email)
                if sender_id:
                    cur.execute("""
                        UPDATE chats
                        SET is_read = TRUE
                        WHERE receiver_id = %s AND sender_id = %s
                    """, (user_id, sender_id))
            else:
                cur.execute("""
                    UPDATE chats
                    SET is_read = TRUE
                    WHERE receiver_id = %s
                """, (user_id,))
        conn.commit()
        conn.close()
    except Exception as e:
        # If is_read column doesn't exist, ignore
        pass

def show_analytics_and_reporting():
    st.subheader("Analytics and Reporting Subsystem")
    st.markdown("**View Reports and Trends:**")

    # Tabs for different analytics
    tab1, tab2, tab3 = st.tabs(["Alumni Career Trends", "Feedback Analysis", "Event Participation Statistics"])

    # Alumni Career Trends
    with tab1:
        st.markdown("### Alumni Career Trends")
        try:
            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT current_position, COUNT(*) AS count
                    FROM users
                    WHERE current_position IS NOT NULL
                    GROUP BY current_position
                    ORDER BY count DESC
                """)
                trends = cur.fetchall()
            conn.close()

            if trends:
                positions = [trend["current_position"] for trend in trends]
                counts = [trend["count"] for trend in trends]
                st.bar_chart({"Positions": positions, "Counts": counts})
            else:
                st.info("No career trend data available.")
        except Exception as e:
            st.error(f"Failed to fetch career trends: {e}")

    # Feedback Analysis
    with tab2:
        st.markdown("### Feedback Analysis")
        try:
            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT feedback_text, COUNT(*) AS count
                    FROM feedback
                    GROUP BY feedback_text
                    ORDER BY count DESC
                """)
                feedback_data = cur.fetchall()
            conn.close()

            if feedback_data:
                # Create a proper chart using pandas and st.bar_chart
                import pandas as pd

                # Prepare data for visualization
                chart_data = pd.DataFrame({
                    'Feedback': [feedback["feedback_text"][:30] + "..." if len(feedback["feedback_text"]) > 30 else feedback["feedback_text"] for feedback in feedback_data],
                    'Count': [feedback["count"] for feedback in feedback_data]
                })

                st.markdown("#### 📊 Feedback Distribution")
                st.bar_chart(chart_data.set_index('Feedback'))

                # Also show as a table for better readability
                st.markdown("#### 📋 Feedback Details")
                st.dataframe(chart_data, use_container_width=True)
            else:
                st.info("No feedback data available.")
        except Exception as e:
            st.error(f"Failed to fetch feedback data: {e}")

    # Event Participation Statistics
    with tab3:
        st.markdown("### Event Participation Statistics")
        try:
            conn = get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT title, COUNT(*) AS participants
                    FROM event_participants
                    JOIN events ON event_participants.event_id = events.id
                    GROUP BY title
                    ORDER BY participants DESC
                """)
                events = cur.fetchall()
            conn.close()

            if events:
                event_titles = [event["title"] for event in events]
                participant_counts = [event["participants"] for event in events]
                st.bar_chart({"Events": event_titles, "Participants": participant_counts})
            else:
                st.info("No event participation data available.")
        except Exception as e:
            st.error(f"Failed to fetch event participation data: {e}")

def show_profile():
    user_email = st.session_state.get("user_email")

    # Enhanced profile header
    st.markdown("""
    <div class="custom-card">
        <h2 style="margin: 0 0 1rem 0;">👤 My Profile</h2>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage your personal information and account settings
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Get user data
    try:
        user_data = get_user_by_email(user_email)
        if not user_data:
            st.error("User data not found. Please contact support.")
            return
    except Exception as e:
        st.error(f"Error loading profile: {e}")
        return

    # Profile layout with columns
    col1, col2 = st.columns([1, 2], gap="large")

    # Left column - Profile picture and basic info
    with col1:
        st.markdown("""
        <div class="custom-card" style="text-align: center;">
            <h4 style="margin: 0 0 1rem 0;">Profile Picture</h4>
        </div>
        """, unsafe_allow_html=True)

        # Profile picture placeholder
        st.markdown(f"""
        <div style="background: linear-gradient(135deg, #667eea, #764ba2);
                    width: 150px; height: 150px; border-radius: 50%;
                    margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
            <span style="color: white; font-size: 3rem; font-weight: bold;">
                {user_data['firstname'][0].upper() if user_data['firstname'] else 'U'}
            </span>
        </div>
        """, unsafe_allow_html=True)

        # Profile picture upload
        uploaded_file = st.file_uploader(
            "Upload New Picture",
            type=["jpg", "jpeg", "png"],
            help="Upload a profile picture (JPG, JPEG, or PNG format)"
        )
        if uploaded_file:
            st.image(uploaded_file, caption="New Profile Picture", use_column_width=True)
            if st.button("Save Profile Picture", use_container_width=True):
                st.success("✅ Profile picture updated successfully!")

        # Quick stats card
        st.markdown("---")
        st.markdown("""
        <div class="custom-card">
            <h5 style="margin: 0 0 1rem 0;">📊 Quick Stats</h5>
        </div>
        """, unsafe_allow_html=True)

        st.metric("Member Since", "2023", "2 years")
        st.metric("Profile Views", "47", "↗️ 12%")
        st.metric("Connections", "23", "↗️ 3")

    # Right column - Profile form
    with col2:
        # Personal Information Section
        st.markdown("""
        <div class="custom-card">
            <h4 style="margin: 0 0 1rem 0;">📝 Personal Information</h4>
        </div>
        """, unsafe_allow_html=True)

        with st.form("profile_form"):
            # Basic Information
            col_first, col_last = st.columns(2)
            with col_first:
                firstname = st.text_input(
                    "First Name",
                    value=user_data.get('firstname', ''),
                    placeholder="Enter your first name"
                )
            with col_last:
                lastname = st.text_input(
                    "Last Name",
                    value=user_data.get('lastname', ''),
                    placeholder="Enter your last name"
                )

            # Contact Information
            email_display = st.text_input(
                "Email Address",
                value=user_data.get('email', ''),
                disabled=True,
                help="Email cannot be changed. Contact admin if needed."
            )

            # Professional Information
            st.markdown("##### 💼 Professional Details")

            col_role, col_dept = st.columns(2)
            with col_role:
                role = st.selectbox(
                    "Role",
                    ["admin", "staff", "student", "alumni"],
                    index=["admin", "staff", "student", "alumni"].index(user_data.get('role', 'student')),
                    disabled=True,
                    help="Role can only be changed by administrators"
                )
            with col_dept:
                department = st.text_input(
                    "Department",
                    value=user_data.get('department', ''),
                    placeholder="e.g., Computer Science"
                )

            current_position = st.text_input(
                "Current Position",
                value=user_data.get('current_position', ''),
                placeholder="e.g., Software Engineer, Student, etc."
            )

            # Conditional fields based on role
            if user_data.get('role') == 'alumni':
                graduation_year = st.number_input(
                    "Graduation Year",
                    min_value=1950,
                    max_value=2030,
                    value=user_data.get('graduation_year', 2023),
                    step=1
                )

            # Interests and Skills
            st.markdown("##### 🎯 Interests & Skills")
            interests_str = ", ".join(user_data.get('interests', [])) if user_data.get('interests') else ""
            interests = st.text_area(
                "Interests & Skills",
                value=interests_str,
                placeholder="Enter your interests and skills (comma-separated)",
                help="e.g., Machine Learning, Web Development, Data Science"
            )

            # Bio/About section
            bio = st.text_area(
                "About Me",
                placeholder="Tell us about yourself, your goals, and what you're passionate about...",
                height=100,
                help="This will be visible to other alumni members"
            )

            # Privacy Settings
            st.markdown("##### 🔒 Privacy Settings")

            col_privacy1, col_privacy2 = st.columns(2)
            with col_privacy1:
                show_email = st.checkbox("Show email to other members", value=False)
                show_graduation = st.checkbox("Show graduation year", value=True)
            with col_privacy2:
                allow_messages = st.checkbox("Allow direct messages", value=True)
                show_in_directory = st.checkbox("Show in alumni directory", value=True)

            # Submit button
            st.markdown('<div style="margin-top: 2rem;"></div>', unsafe_allow_html=True)

            col_save, col_cancel = st.columns([2, 1])
            with col_save:
                save_btn = st.form_submit_button("💾 Save Changes", use_container_width=True)
            with col_cancel:
                cancel_btn = st.form_submit_button("❌ Cancel", use_container_width=True)

            if save_btn:
                try:
                    # Prepare update data
                    update_data = {
                        "firstname": firstname,
                        "lastname": lastname,
                        "department": department,
                        "current_position": current_position,
                        "interests": [interest.strip() for interest in interests.split(",") if interest.strip()]
                    }

                    # Add graduation year if alumni
                    if user_data.get('role') == 'alumni':
                        update_data["graduation_year"] = graduation_year

                    # Update user in database
                    update_user_details(user_email, **update_data)
                    st.success("✅ Profile updated successfully!")
                    st.rerun()

                except Exception as e:
                    st.error(f"❌ Error updating profile: {e}")

            if cancel_btn:
                st.info("Changes cancelled.")
                st.rerun()

    # Update Password
    st.markdown("### Update Password")
    with st.form("update_password_form"):
        current_password = st.text_input("Current Password", type="password", placeholder="Enter your current password")
        new_password = st.text_input("New Password", type="password", placeholder="Enter your new password")
        confirm_password = st.text_input("Confirm New Password", type="password", placeholder="Confirm your new password")
        update_password_btn = st.form_submit_button("Update Password")

        if update_password_btn:
            if not current_password or not new_password or not confirm_password:
                st.error("All fields are required.")
            elif new_password != confirm_password:
                st.error("New password and confirmation do not match.")
            else:
                email = st.session_state.get("user_email")
                if update_password_in_db(email, current_password, new_password):
                    st.success("Password updated successfully!")
                    # Clear form by rerunning
                    st.rerun()
                else:
                    st.error("Failed to update password. Current password may be incorrect.")

# Helper function to search users by email or name
def search_users(query):
    """
    Search for users by email or name in the database.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT email, firstname, lastname FROM users
                WHERE email ILIKE %s OR firstname ILIKE %s OR lastname ILIKE %s
            """, (f"%{query}%", f"%{query}%", f"%{query}%"))
            users = cur.fetchall()
        conn.close()
        return users
    except Exception as e:
        st.error(f"Failed to search users: {e}")
        return []

# Helper function to get user name by email
def get_user_name_by_email(email):
    """
    Fetch the user's full name by email.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT firstname, lastname FROM users WHERE email = %s", (email,))
            user = cur.fetchone()
        conn.close()
        return f"{user['firstname']} {user['lastname']}" if user else "Unknown"
    except Exception as e:
        st.error(f"Failed to fetch user name: {e}")
        return "Unknown"

def get_user_name_by_email_from_id(user_id):
    """
    Fetch the user's full name by user ID.
    """
    try:
        conn = get_db_connection()
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("SELECT firstname, lastname FROM users WHERE id = %s", (user_id,))
            user = cur.fetchone()
        conn.close()
        return f"{user['firstname']} {user['lastname']}" if user else "Unknown"
    except Exception as e:
        return "Unknown"

def show_messages_inbox(user_email):
    """Show user's message inbox"""
    from communication_system import show_user_messages_inbox
    show_user_messages_inbox(user_email)

def show_admin_tools(admin_email):
    """Show admin tools dashboard"""
    st.subheader("🔧 Admin Tools")

    tab1, tab2, tab3 = st.tabs(["📊 System Dashboard", "📋 Audit Log", "🗑️ Registration Management"])

    with tab1:
        from admin_management import show_system_dashboard
        show_system_dashboard()

    with tab2:
        from admin_management import show_audit_log
        show_audit_log()

    with tab3:
        st.markdown("### 🗑️ Registration Management")

        # Get all events for selection
        try:
            conn = get_connection()
            cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
            cur.execute("SELECT id, title, event_date FROM events ORDER BY event_date DESC")
            events = cur.fetchall()
            conn.close()

            if events:
                event_options = {f"{event['title']} ({event['event_date']})": event for event in events}
                selected_event_name = st.selectbox("Select Event:", list(event_options.keys()))

                if selected_event_name:
                    selected_event = event_options[selected_event_name]
                    from admin_management import show_registration_management
                    show_registration_management(
                        selected_event['id'],
                        selected_event['title'],
                        admin_email
                    )
            else:
                st.info("No events found")

        except Exception as e:
            st.error(f"Error loading events: {e}")

def show_events_subsystem(role, user_email):
    """Enhanced events subsystem with professional UI"""
    if UI_COMPONENTS_AVAILABLE:
        # Use the enhanced event management system
        show_events_dashboard(user_email, role)
    else:
        # Fallback to basic event system
        st.subheader("🎉 Events Subsystem")
        if role == "admin":
            # Admin can manage events
            st.markdown("### Manage Events")
            tab1, tab2, tab3 = st.tabs(["Add Event", "Edit Event", "Delete Event"])

            # Add Event Tab
            with tab1:
                st.markdown("#### Add a New Event")
                with st.form("add_event_form"):
                    title = st.text_input("Event Title", placeholder="Enter event title")
                    event_date = st.date_input("Event Date")
                    event_description = st.text_area("Event Description", placeholder="Enter event description")
                    add_event_btn = st.form_submit_button("Add Event")

                    if add_event_btn:
                        if title and event_date and event_description:
                            try:
                                conn = get_db_connection()
                                with conn.cursor() as cur:
                                    cur.execute("""
                                        INSERT INTO events (title, event_date, description)
                                        VALUES (%s, %s, %s)
                                    """, (title, event_date, event_description))
                                conn.commit()
                                conn.close()
                                st.success(f"Event '{title}' added successfully!")
                                # Clear form by resetting session state
                                for key in st.session_state.keys():
                                    if key.startswith('add_event_form-'):
                                        del st.session_state[key]
                                time.sleep(0.8)  # Brief pause to show success message
                                st.rerun()  # Refresh the page to reset the form
                            except Exception as e:
                                st.error(f"Failed to add event: {e}")
                        else:
                            st.error("All fields are required.")

            # Edit Event Tab
            with tab2:
                st.markdown("#### Edit an Existing Event")
                event_id = st.text_input("Event ID", placeholder="Enter event ID to edit")
                fetch_event_btn = st.button("Fetch Event Details")

                if fetch_event_btn:
                    if event_id:
                        try:
                            conn = get_db_connection()
                            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                                cur.execute("SELECT * FROM events WHERE id = %s", (event_id,))
                                event = cur.fetchone()
                            conn.close()

                            if event:
                                st.success(f"Event '{event['title']}' found. You can now edit its details.")
                                title = st.text_input("Event Title", value=event["title"])
                                event_date = st.date_input("Event Date", value=event["event_date"])
                                event_description = st.text_area("Event Description", value=event["description"])
                                update_event_btn = st.button("Update Event")

                                if update_event_btn:
                                    try:
                                        conn = get_db_connection()
                                        with conn.cursor() as cur:
                                            cur.execute("""
                                                UPDATE events
                                                SET title = %s, event_date = %s, description = %s
                                                WHERE id = %s
                                            """, (title, event_date, event_description, event_id))
                                        conn.commit()
                                        conn.close()
                                        st.success(f"Event '{title}' updated successfully!")
                                    except Exception as e:
                                        st.error(f"Failed to update event: {e}")
                            else:
                                st.error(f"No event found with ID {event_id}.")
                        except Exception as e:
                            st.error(f"Failed to fetch event details: {e}")
                    else:
                        st.error("Please provide the event ID.")

            # Delete Event Tab
            with tab3:
                st.markdown("#### Delete an Event")
                event_id = st.text_input("Event ID to Delete", placeholder="Enter event ID")
                delete_event_btn = st.button("Delete Event")

                if delete_event_btn:
                    if event_id:
                        try:
                            conn = get_db_connection()
                            with conn.cursor() as cur:
                                cur.execute("DELETE FROM events WHERE id = %s", (event_id,))
                            conn.commit()
                            conn.close()
                            st.success(f"Event with ID {event_id} deleted successfully!")
                        except Exception as e:
                            st.error(f"Failed to delete event: {e}")
                    else:
                        st.error("Please provide the event ID.")

        else:
            # Non-admin users can view and register for events
            st.markdown("### View and Register for Events")
            try:
                conn = get_db_connection()
                with conn.cursor(cursor_factory=RealDictCursor) as cur:
                    cur.execute("SELECT * FROM events ORDER BY event_date ASC")
                    events = cur.fetchall()
                conn.close()

                if events:
                    for event in events:
                        st.markdown(f"**Event Name:** {event['title']}")
                        st.markdown(f"**Date:** {event['event_date']}")
                        st.markdown(f"**Description:** {event['description']}")
                        register_btn = st.button(f"Register for {event['title']}", key=f"register_{event['id']}")

                        if register_btn:
                            # Get the user_id from the email first
                            user_id = get_user_id_by_email(user_email)
                            if user_id:
                                try:
                                    conn = get_db_connection()
                                    with conn.cursor() as cur:
                                        # First check if the user is already registered
                                        cur.execute("""
                                            SELECT 1 FROM event_participants
                                            WHERE event_id = %s AND user_id = %s
                                        """, (event["id"], user_id))
                                        already_registered = cur.fetchone()

                                        if already_registered:
                                            st.warning(f"You have already registered for '{event['title']}'!")
                                        else:
                                            cur.execute("""
                                                INSERT INTO event_participants (event_id, user_id)
                                                VALUES (%s, %s)
                                            """, (event["id"], user_id))
                                            conn.commit()
                                            st.success(f"Successfully registered for '{event['title']}'!")
                                    conn.close()
                                except Exception as e:
                                    st.error(f"Failed to register for event: {e}")
                            else:
                                st.error("Could not find your user ID. Registration failed.")
                else:
                    st.info("No events available.")
            except Exception as e:
                st.error(f"Failed to fetch events: {e}")

def train_model_in_background():
    """
    Train the recommendation model in the background when the application starts.
    """
    def background_task():
        st.info("Training recommendation models in the background...")
        train_and_select_best_model()

    thread = threading.Thread(target=background_task, daemon=True)
    thread.start()

# Modify the main function to trigger background training
def main():
    set_page_config()
    load_css()
    
    # Trigger model training in the background
    train_model_in_background()

    # Simplified page navigation - only landing and dashboard
    if not st.session_state.authenticated:
        show_landing_page()
    else:
        show_dashboard()

if __name__ == "__main__":
    main()
