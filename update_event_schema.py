#!/usr/bin/env python3
"""
Update event database schema for enhanced functionality
"""

from db import get_connection
import psycopg2.extras

def update_event_schema():
    """Update events and event_participants tables with proper data types and columns"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print("🔧 Updating event database schema...")
        
        # Check current events table structure
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'events'
            ORDER BY ordinal_position
        """)
        events_columns = cur.fetchall()
        print(f"\nCurrent EVENTS table structure:")
        for col in events_columns:
            print(f"  {col['column_name']} ({col['data_type']}) - Nullable: {col['is_nullable']}")
        
        # Add missing columns to events table
        missing_events_columns = []
        
        existing_columns = [col['column_name'] for col in events_columns]
        
        if 'registration_deadline' not in existing_columns:
            missing_events_columns.append("registration_deadline DATE")
        
        if 'max_participants' not in existing_columns:
            missing_events_columns.append("max_participants INTEGER")
        
        if 'created_at' not in existing_columns:
            missing_events_columns.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        
        if 'updated_at' not in existing_columns:
            missing_events_columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        
        # Add missing columns
        for column in missing_events_columns:
            try:
                cur.execute(f"ALTER TABLE events ADD COLUMN {column}")
                print(f"  ✅ Added column: {column}")
            except Exception as e:
                print(f"  ⚠️ Column might already exist: {column} - {e}")
        
        # Check event_participants table structure
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'event_participants'
            ORDER BY ordinal_position
        """)
        participants_columns = cur.fetchall()
        print(f"\nCurrent EVENT_PARTICIPANTS table structure:")
        for col in participants_columns:
            print(f"  {col['column_name']} ({col['data_type']}) - Nullable: {col['is_nullable']}")
        
        # Add missing columns to event_participants table
        missing_participants_columns = []
        
        existing_participants_columns = [col['column_name'] for col in participants_columns]
        
        if 'registered_at' not in existing_participants_columns:
            missing_participants_columns.append("registered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        
        if 'status' not in existing_participants_columns:
            missing_participants_columns.append("status VARCHAR(20) DEFAULT 'registered'")
        
        if 'updated_at' not in existing_participants_columns:
            missing_participants_columns.append("updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
        
        # Add missing columns
        for column in missing_participants_columns:
            try:
                cur.execute(f"ALTER TABLE event_participants ADD COLUMN {column}")
                print(f"  ✅ Added column: {column}")
            except Exception as e:
                print(f"  ⚠️ Column might already exist: {column} - {e}")
        
        # Create indexes for better performance
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_events_date ON events(event_date)",
            "CREATE INDEX IF NOT EXISTS idx_events_status ON events(registration_deadline, event_date)",
            "CREATE INDEX IF NOT EXISTS idx_participants_event ON event_participants(event_id)",
            "CREATE INDEX IF NOT EXISTS idx_participants_user ON event_participants(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_participants_status ON event_participants(status)"
        ]
        
        print(f"\nCreating performance indexes...")
        for index_sql in indexes:
            try:
                cur.execute(index_sql)
                print(f"  ✅ Created index: {index_sql.split('idx_')[1].split(' ')[0]}")
            except Exception as e:
                print(f"  ⚠️ Index might already exist: {e}")
        
        # Add constraints for data integrity
        constraints = [
            "ALTER TABLE events ADD CONSTRAINT chk_event_date_future CHECK (event_date >= CURRENT_DATE - INTERVAL '1 year')",
            "ALTER TABLE events ADD CONSTRAINT chk_registration_deadline CHECK (registration_deadline IS NULL OR registration_deadline <= event_date)",
            "ALTER TABLE events ADD CONSTRAINT chk_max_participants_positive CHECK (max_participants IS NULL OR max_participants > 0)",
            "ALTER TABLE event_participants ADD CONSTRAINT chk_status_valid CHECK (status IN ('registered', 'cancelled', 'attended', 'no_show'))"
        ]
        
        print(f"\nAdding data integrity constraints...")
        for constraint_sql in constraints:
            try:
                cur.execute(constraint_sql)
                constraint_name = constraint_sql.split('CONSTRAINT ')[1].split(' ')[0]
                print(f"  ✅ Added constraint: {constraint_name}")
            except Exception as e:
                print(f"  ⚠️ Constraint might already exist: {e}")
        
        # Update existing data to have proper defaults
        print(f"\nUpdating existing data...")
        
        # Set default registration status for existing participants
        cur.execute("""
            UPDATE event_participants 
            SET status = 'registered' 
            WHERE status IS NULL
        """)
        updated_participants = cur.rowcount
        print(f"  ✅ Updated {updated_participants} participant records with default status")
        
        # Set created_at for existing events
        cur.execute("""
            UPDATE events 
            SET created_at = CURRENT_TIMESTAMP 
            WHERE created_at IS NULL
        """)
        updated_events = cur.rowcount
        print(f"  ✅ Updated {updated_events} event records with created_at timestamp")
        
        conn.commit()
        
        # Show final table structures
        print(f"\n📊 Final EVENTS table structure:")
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'events'
            ORDER BY ordinal_position
        """)
        final_events_columns = cur.fetchall()
        for col in final_events_columns:
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"  {col['column_name']} ({col['data_type']}) - Nullable: {col['is_nullable']}{default}")
        
        print(f"\n📊 Final EVENT_PARTICIPANTS table structure:")
        cur.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'event_participants'
            ORDER BY ordinal_position
        """)
        final_participants_columns = cur.fetchall()
        for col in final_participants_columns:
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"  {col['column_name']} ({col['data_type']}) - Nullable: {col['is_nullable']}{default}")
        
        # Show sample data
        print(f"\n📋 Sample events data:")
        cur.execute("SELECT id, title, event_date, registration_deadline, max_participants FROM events LIMIT 3")
        sample_events = cur.fetchall()
        for event in sample_events:
            print(f"  Event {event['id']}: {event['title']} - {event['event_date']} (Deadline: {event['registration_deadline']}, Max: {event['max_participants']})")
        
        print(f"\n📋 Sample participants data:")
        cur.execute("""
            SELECT ep.event_id, ep.user_id, ep.status, ep.registered_at, u.email
            FROM event_participants ep
            JOIN users u ON ep.user_id = u.id
            LIMIT 3
        """)
        sample_participants = cur.fetchall()
        for participant in sample_participants:
            print(f"  Event {participant['event_id']}: {participant['email']} - {participant['status']} ({participant['registered_at']})")
        
        conn.close()
        print(f"\n✅ Event database schema update completed successfully!")
        
    except Exception as e:
        print(f"❌ Error updating event schema: {e}")

def test_event_functionality():
    """Test the enhanced event functionality"""
    try:
        conn = get_connection()
        cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        print(f"\n🧪 Testing enhanced event functionality...")
        
        # Test 1: Create a test event with all new fields
        print(f"\n1. Testing event creation with new fields...")
        test_event_data = {
            'title': 'Test Enhanced Event',
            'description': 'Testing the enhanced event system with all new features',
            'event_date': '2025-08-15',
            'location': 'Test Location',
            'registration_deadline': '2025-08-10',
            'max_participants': 25
        }
        
        cur.execute("""
            INSERT INTO events (title, description, event_date, location, registration_deadline, max_participants)
            VALUES (%(title)s, %(description)s, %(event_date)s, %(location)s, %(registration_deadline)s, %(max_participants)s)
            RETURNING id
        """, test_event_data)
        
        test_event_id = cur.fetchone()['id']
        print(f"  ✅ Created test event with ID: {test_event_id}")
        
        # Test 2: Register users for the event
        print(f"\n2. Testing event registration...")
        cur.execute("SELECT id, email FROM users LIMIT 2")
        test_users = cur.fetchall()
        
        for user in test_users:
            cur.execute("""
                INSERT INTO event_participants (event_id, user_id, status)
                VALUES (%s, %s, 'registered')
            """, (test_event_id, user['id']))
            print(f"  ✅ Registered user {user['email']} for test event")
        
        # Test 3: Query events with registration counts
        print(f"\n3. Testing event queries with registration counts...")
        cur.execute("""
            SELECT 
                e.title,
                e.event_date,
                e.registration_deadline,
                e.max_participants,
                COUNT(ep.user_id) as registration_count,
                CASE 
                    WHEN e.registration_deadline < CURRENT_DATE THEN 'closed'
                    WHEN e.event_date < CURRENT_DATE THEN 'completed'
                    ELSE 'open'
                END as status
            FROM events e
            LEFT JOIN event_participants ep ON e.id = ep.event_id
            WHERE e.id = %s
            GROUP BY e.id, e.title, e.event_date, e.registration_deadline, e.max_participants
        """, (test_event_id,))
        
        test_result = cur.fetchone()
        print(f"  ✅ Event query result:")
        print(f"    Title: {test_result['title']}")
        print(f"    Date: {test_result['event_date']}")
        print(f"    Deadline: {test_result['registration_deadline']}")
        print(f"    Max Participants: {test_result['max_participants']}")
        print(f"    Current Registrations: {test_result['registration_count']}")
        print(f"    Status: {test_result['status']}")
        
        # Clean up test data
        print(f"\n4. Cleaning up test data...")
        cur.execute("DELETE FROM event_participants WHERE event_id = %s", (test_event_id,))
        cur.execute("DELETE FROM events WHERE id = %s", (test_event_id,))
        print(f"  ✅ Test data cleaned up")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ All event functionality tests passed!")
        
    except Exception as e:
        print(f"❌ Error testing event functionality: {e}")

def main():
    update_event_schema()
    test_event_functionality()

if __name__ == "__main__":
    main()
