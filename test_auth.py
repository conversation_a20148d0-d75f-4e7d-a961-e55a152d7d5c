#!/usr/bin/env python3
"""
Script to test authentication system
"""

from db import get_connection, authenticate_user, get_user_permissions
import psycopg2.extras
import bcrypt

def test_authentication():
    print("Testing Authentication System...")
    
    # Test users from the database
    test_users = [
        ("<EMAIL>", "admin123"),
        ("<EMAIL>", "staff123"),
        ("<EMAIL>", "student123"),
        ("<EMAIL>", "salma123")
    ]
    
    for email, password in test_users:
        print(f"\nTesting login for {email}...")
        
        # Test authentication
        auth_result = authenticate_user(email, password)
        print(f"Authentication result: {auth_result}")
        
        if auth_result:
            # Test permissions
            permissions = get_user_permissions(email)
            print(f"Permissions: {permissions}")
        else:
            print("Authentication failed - checking password hash...")
            
            # Check what's in the database
            try:
                conn = get_connection()
                cur = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
                cur.execute("SELECT password_hash FROM users WHERE email = %s", (email,))
                result = cur.fetchone()
                if result:
                    stored_hash = result['password_hash']
                    print(f"Stored hash exists: {len(stored_hash)} characters")
                    
                    # Try to verify with bcrypt
                    try:
                        check_result = bcrypt.checkpw(password.encode(), stored_hash.encode())
                        print(f"Manual bcrypt check: {check_result}")
                    except Exception as e:
                        print(f"Bcrypt check error: {e}")
                else:
                    print("No user found with this email")
                conn.close()
            except Exception as e:
                print(f"Database error: {e}")

def test_user_creation():
    print("\n\nTesting User Creation...")
    
    # Test creating a new user
    test_email = "<EMAIL>"
    test_password = "test123"
    
    try:
        # Hash password
        hashed_pw = bcrypt.hashpw(test_password.encode(), bcrypt.gensalt()).decode()
        
        conn = get_connection()
        cur = conn.cursor()
        
        # Check if user already exists
        cur.execute("SELECT id FROM users WHERE email = %s", (test_email,))
        if cur.fetchone():
            print(f"Test user {test_email} already exists, deleting first...")
            cur.execute("DELETE FROM users WHERE email = %s", (test_email,))
        
        # Create new user
        cur.execute("""
            INSERT INTO users (firstname, lastname, email, password_hash, role, department)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, ("Test", "User", test_email, hashed_pw, "student", "Computer Science"))
        
        conn.commit()
        print(f"Created test user: {test_email}")
        
        # Test authentication with new user
        auth_result = authenticate_user(test_email, test_password)
        print(f"Authentication test for new user: {auth_result}")
        
        # Clean up
        cur.execute("DELETE FROM users WHERE email = %s", (test_email,))
        conn.commit()
        print("Cleaned up test user")
        
        conn.close()
        
    except Exception as e:
        print(f"Error testing user creation: {e}")

if __name__ == "__main__":
    test_authentication()
    test_user_creation()
